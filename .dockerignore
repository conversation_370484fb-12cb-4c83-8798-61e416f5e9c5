# RapidTrader Docker Ignore File
# Excludes unnecessary files from Docker build context for faster builds and smaller images

# Version Control
.git
.gitignore
.gitattributes

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environments
venv/
env/
ENV/
.venv/
.env/
pip-log.txt
pip-delete-this-directory.txt

# IDE and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Documentation (not needed in runtime)
docs/
*.md
README*
LICENSE
CHANGELOG*
HISTORY*
AUTHORS*
CONTRIBUTORS*

# Test Files
test/
tests/
*_test.py
test_*.py
*.test.py
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/

# Development and Build Files
Makefile
*.mk
.travis.yml
.github/
.gitlab-ci.yml
Jenkinsfile
azure-pipelines.yml
appveyor.yml
tox.ini
setup.py
setup.cfg
pyproject.toml
requirements-dev.txt
requirements.txt.backup
requirements.optimized.txt

# Frontend Development (not needed for backend)
frontend/
frontend_v2/
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock
.npm
.eslintcache

# Project-specific unwanted directories
project-bolt-*/
demo/
examples/
scripts/test-*.sh
scripts/build-*.sh

# Logs and Runtime Data
*.log
logs/
userdata/logs/
userdata/historical_data/
userdata/results/
.cache/

# Temporary Files
tmp/
temp/
.tmp/
*.tmp
*.temp

# OS Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker Files (except the one being used)
Dockerfile.old
docker-compose.*.yml
!docker-compose.yml
.dockerignore.old

# Backup Files
*.bak
*.backup
*.old
*~

# Large Data Files
*.csv
*.json.bak*
data/symbols/*.csv
data/symbols/*.bak*

# Compiled Files
*.pyc
*.pyo
*.pyd
*.so
*.dll
*.exe

# Configuration Files with Secrets
.env
.env.local
.env.production
.env.staging
*.key
*.pem
*.p12
*.pfx

# Database Files
*.db
*.sqlite
*.sqlite3

# Jupyter Notebooks
*.ipynb
.ipynb_checkpoints/

# PyCharm
.idea/

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/

# Custom exclusions for RapidTrader
ACCOMPLISHMENTS_SUMMARY.md
API_INTEGRATION_COMPLETE.md
BROKER_SYMBOL_MANAGEMENT_COMPLETE.md
DEVELOPER_INTEGRATION_GUIDE.md
OPTIMIZATION_SUMMARY.md
demo_broker_symbol_integration.py
test_broker_symbol_manager.py
test_enhanced_symbol_manager.py

# Keep only essential files for runtime
# Include: core/, broker/, data/, api_gateway/, config/, money_management/, 
#          security/, telemetry/, ui_module/, userdata/, requirements.txt, rapidtrader
