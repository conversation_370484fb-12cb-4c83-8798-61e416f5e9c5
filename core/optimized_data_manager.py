"""
Optimized Data Manager for RapidTrader
High-performance data handling with caching, vectorization, and memory optimization
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import asyncio
import aiohttp
import json
import pickle
import gzip
from concurrent.futures import ThreadPoolExecutor
import threading
from functools import lru_cache
import weakref

logger = logging.getLogger(__name__)

class OptimizedDataManager:
    """High-performance data manager with advanced caching and optimization"""
    
    def __init__(self, data_dir: str = "userdata/data", cache_size: int = 1000):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Memory-efficient caching
        self._memory_cache = {}
        self._cache_timestamps = {}
        self._cache_size = cache_size
        self._cache_lock = threading.RLock()
        
        # Disk cache for persistence
        self.disk_cache_dir = self.data_dir / "cache"
        self.disk_cache_dir.mkdir(exist_ok=True)
        
        # Connection pooling for async operations
        self._session = None
        self._executor = ThreadPoolExecutor(max_workers=4)
        
        # Data type optimization
        self.optimized_dtypes = {
            'open': 'float32',
            'high': 'float32',
            'low': 'float32', 
            'close': 'float32',
            'volume': 'int32',
            'timestamp': 'datetime64[ns]'
        }
        
        logger.info("OptimizedDataManager initialized with enhanced caching")
    
    async def get_session(self):
        """Get or create async HTTP session with connection pooling"""
        if self._session is None or self._session.closed:
            connector = aiohttp.TCPConnector(
                limit=100,
                limit_per_host=30,
                ttl_dns_cache=300,
                use_dns_cache=True,
                keepalive_timeout=30
            )
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            self._session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            )
        return self._session
    
    @lru_cache(maxsize=128)
    def _get_cache_key(self, symbol: str, timeframe: str, start_date: str, end_date: str) -> str:
        """Generate optimized cache key"""
        return f"{symbol}_{timeframe}_{start_date}_{end_date}"
    
    def _is_cache_valid(self, cache_key: str, max_age_minutes: int = 15) -> bool:
        """Check if cached data is still valid"""
        if cache_key not in self._cache_timestamps:
            return False
        
        age = datetime.now() - self._cache_timestamps[cache_key]
        return age.total_seconds() < (max_age_minutes * 60)
    
    def _optimize_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Optimize DataFrame memory usage"""
        if df.empty:
            return df
        
        # Convert to optimal dtypes
        for col, dtype in self.optimized_dtypes.items():
            if col in df.columns:
                if dtype.startswith('float'):
                    df[col] = pd.to_numeric(df[col], errors='coerce').astype(dtype)
                elif dtype.startswith('int'):
                    df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0).astype(dtype)
        
        # Optimize index
        if 'date' in df.columns and df.index.name != 'date':
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)
        
        return df
    
    def _save_to_disk_cache(self, cache_key: str, data: pd.DataFrame):
        """Save data to compressed disk cache"""
        try:
            cache_file = self.disk_cache_dir / f"{cache_key}.pkl.gz"
            with gzip.open(cache_file, 'wb') as f:
                pickle.dump(data, f, protocol=pickle.HIGHEST_PROTOCOL)
        except Exception as e:
            logger.warning(f"Failed to save disk cache for {cache_key}: {e}")
    
    def _load_from_disk_cache(self, cache_key: str) -> Optional[pd.DataFrame]:
        """Load data from compressed disk cache"""
        try:
            cache_file = self.disk_cache_dir / f"{cache_key}.pkl.gz"
            if cache_file.exists():
                # Check file age
                file_age = datetime.now() - datetime.fromtimestamp(cache_file.stat().st_mtime)
                if file_age.total_seconds() < 3600:  # 1 hour
                    with gzip.open(cache_file, 'rb') as f:
                        return pickle.load(f)
        except Exception as e:
            logger.warning(f"Failed to load disk cache for {cache_key}: {e}")
        return None
    
    def get_data_fast(self, symbol: str, timeframe: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """
        Get data with multi-level caching for maximum speed
        
        Cache hierarchy:
        1. Memory cache (fastest)
        2. Disk cache (fast)
        3. File system (slower)
        4. Download (slowest)
        """
        cache_key = self._get_cache_key(symbol, timeframe, start_date, end_date)
        
        # Level 1: Memory cache
        with self._cache_lock:
            if cache_key in self._memory_cache and self._is_cache_valid(cache_key):
                logger.debug(f"Memory cache hit for {symbol}")
                return self._memory_cache[cache_key]
        
        # Level 2: Disk cache
        data = self._load_from_disk_cache(cache_key)
        if data is not None:
            logger.debug(f"Disk cache hit for {symbol}")
            with self._cache_lock:
                self._memory_cache[cache_key] = data
                self._cache_timestamps[cache_key] = datetime.now()
                self._cleanup_cache()
            return data
        
        # Level 3: File system
        data = self._load_from_file(symbol, timeframe, start_date, end_date)
        if data is not None:
            data = self._optimize_dataframe(data)
            
            # Cache the data
            with self._cache_lock:
                self._memory_cache[cache_key] = data
                self._cache_timestamps[cache_key] = datetime.now()
                self._cleanup_cache()
            
            # Save to disk cache asynchronously
            self._executor.submit(self._save_to_disk_cache, cache_key, data)
            
            return data
        
        logger.warning(f"No data found for {symbol}")
        return None
    
    def _load_from_file(self, symbol: str, timeframe: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """Load data from file with optimizations"""
        data_file = self.data_dir / f"{symbol}_{timeframe}.csv"
        
        if not data_file.exists():
            return None
        
        try:
            # Use optimized reading
            df = pd.read_csv(
                data_file,
                dtype=self.optimized_dtypes,
                parse_dates=['date'],
                date_parser=pd.to_datetime,
                engine='c'  # Use C engine for speed
            )
            
            # Efficient date filtering
            if start_date:
                start_dt = pd.to_datetime(start_date)
                df = df[df['date'] >= start_dt]
            
            if end_date:
                end_dt = pd.to_datetime(end_date)
                df = df[df['date'] <= end_dt]
            
            return df
            
        except Exception as e:
            logger.error(f"Error loading data for {symbol}: {e}")
            return None
    
    def _cleanup_cache(self):
        """Clean up memory cache when it gets too large"""
        if len(self._memory_cache) > self._cache_size:
            # Remove oldest entries
            sorted_items = sorted(
                self._cache_timestamps.items(),
                key=lambda x: x[1]
            )
            
            # Remove oldest 20% of entries
            remove_count = len(sorted_items) // 5
            for cache_key, _ in sorted_items[:remove_count]:
                self._memory_cache.pop(cache_key, None)
                self._cache_timestamps.pop(cache_key, None)
    
    async def preload_data_async(self, symbols: List[str], timeframe: str, start_date: str, end_date: str):
        """Preload data asynchronously for multiple symbols"""
        tasks = []
        for symbol in symbols:
            task = asyncio.create_task(
                asyncio.get_event_loop().run_in_executor(
                    self._executor,
                    self.get_data_fast,
                    symbol, timeframe, start_date, end_date
                )
            )
            tasks.append(task)
        
        await asyncio.gather(*tasks, return_exceptions=True)
        logger.info(f"Preloaded data for {len(symbols)} symbols")
    
    def get_memory_usage(self) -> Dict[str, Any]:
        """Get current memory usage statistics"""
        total_size = 0
        for df in self._memory_cache.values():
            if isinstance(df, pd.DataFrame):
                total_size += df.memory_usage(deep=True).sum()
        
        return {
            "cached_items": len(self._memory_cache),
            "memory_usage_mb": total_size / (1024 * 1024),
            "cache_hit_ratio": getattr(self, '_cache_hits', 0) / max(getattr(self, '_cache_requests', 1), 1)
        }
    
    def clear_cache(self):
        """Clear all caches"""
        with self._cache_lock:
            self._memory_cache.clear()
            self._cache_timestamps.clear()
        
        # Clear disk cache
        for cache_file in self.disk_cache_dir.glob("*.pkl.gz"):
            try:
                cache_file.unlink()
            except Exception as e:
                logger.warning(f"Failed to delete cache file {cache_file}: {e}")
        
        logger.info("All caches cleared")
    
    async def close(self):
        """Clean up resources"""
        if self._session and not self._session.closed:
            await self._session.close()
        
        self._executor.shutdown(wait=True)
        logger.info("OptimizedDataManager closed")
