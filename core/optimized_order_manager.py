"""
Optimized Order Manager for RapidTrader
High-performance order execution with connection pooling, batching, and async processing
"""

import logging
import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import json
import threading
from concurrent.futures import ThreadPoolExecutor
import time
from collections import deque
import weakref

logger = logging.getLogger(__name__)

class OrderStatus(Enum):
    PENDING = "PENDING"
    SUBMITTED = "SUBMITTED"
    FILLED = "FILLED"
    PARTIALLY_FILLED = "PARTIALLY_FILLED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"

class OrderType(Enum):
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP_LOSS = "STOP_LOSS"
    STOP_LIMIT = "STOP_LIMIT"

@dataclass
class OptimizedOrder:
    """Optimized order structure for fast processing"""
    symbol: str
    quantity: int
    side: str  # BUY/SELL
    order_type: OrderType
    price: Optional[float] = None
    stop_price: Optional[float] = None
    order_id: Optional[str] = None
    status: OrderStatus = OrderStatus.PENDING
    timestamp: Optional[datetime] = None
    filled_quantity: int = 0
    avg_fill_price: float = 0.0
    broker_order_id: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class OptimizedOrderManager:
    """High-performance order manager with batching and async processing"""
    
    def __init__(self, broker_interface, max_batch_size: int = 10, batch_timeout: float = 0.1):
        self.broker = broker_interface
        self.max_batch_size = max_batch_size
        self.batch_timeout = batch_timeout
        
        # Order tracking
        self.orders: Dict[str, OptimizedOrder] = {}
        self.pending_orders = deque()
        self.order_lock = threading.RLock()
        
        # Batch processing
        self.batch_queue = asyncio.Queue()
        self.batch_processor_task = None
        self.is_running = False
        
        # Connection pooling
        self._session = None
        self._executor = ThreadPoolExecutor(max_workers=8)
        
        # Performance metrics
        self.metrics = {
            "orders_placed": 0,
            "orders_filled": 0,
            "orders_cancelled": 0,
            "avg_execution_time": 0.0,
            "batch_efficiency": 0.0
        }
        
        # Rate limiting
        self.rate_limiter = {
            "requests_per_second": 10,
            "last_request_time": 0,
            "request_count": 0
        }
        
        logger.info("OptimizedOrderManager initialized")
    
    async def start(self):
        """Start the order manager and batch processor"""
        if self.is_running:
            return
        
        self.is_running = True
        self.batch_processor_task = asyncio.create_task(self._batch_processor())
        logger.info("Order manager started")
    
    async def stop(self):
        """Stop the order manager"""
        self.is_running = False
        
        if self.batch_processor_task:
            self.batch_processor_task.cancel()
            try:
                await self.batch_processor_task
            except asyncio.CancelledError:
                pass
        
        if self._session and not self._session.closed:
            await self._session.close()
        
        self._executor.shutdown(wait=True)
        logger.info("Order manager stopped")
    
    async def place_order_async(self, order: OptimizedOrder) -> str:
        """Place order asynchronously with batching"""
        order.order_id = self._generate_order_id()
        order.timestamp = datetime.now()
        
        with self.order_lock:
            self.orders[order.order_id] = order
        
        # Add to batch queue for processing
        await self.batch_queue.put(order)
        
        logger.debug(f"Order {order.order_id} queued for {order.symbol}")
        return order.order_id
    
    def place_order_sync(self, order: OptimizedOrder) -> str:
        """Synchronous wrapper for order placement"""
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If we're already in an async context, create a task
            future = asyncio.create_task(self.place_order_async(order))
            return order.order_id
        else:
            # Run in new event loop
            return loop.run_until_complete(self.place_order_async(order))
    
    async def _batch_processor(self):
        """Process orders in batches for efficiency"""
        batch = []
        last_batch_time = time.time()
        
        while self.is_running:
            try:
                # Wait for orders with timeout
                try:
                    order = await asyncio.wait_for(
                        self.batch_queue.get(),
                        timeout=self.batch_timeout
                    )
                    batch.append(order)
                except asyncio.TimeoutError:
                    pass
                
                current_time = time.time()
                should_process = (
                    len(batch) >= self.max_batch_size or
                    (batch and (current_time - last_batch_time) >= self.batch_timeout)
                )
                
                if should_process and batch:
                    await self._process_batch(batch)
                    batch.clear()
                    last_batch_time = current_time
                
            except Exception as e:
                logger.error(f"Error in batch processor: {e}")
                await asyncio.sleep(0.1)
    
    async def _process_batch(self, batch: List[OptimizedOrder]):
        """Process a batch of orders efficiently"""
        start_time = time.time()
        
        # Group orders by type for optimal processing
        market_orders = [o for o in batch if o.order_type == OrderType.MARKET]
        limit_orders = [o for o in batch if o.order_type == OrderType.LIMIT]
        
        # Process market orders first (higher priority)
        if market_orders:
            await self._execute_orders_batch(market_orders)
        
        if limit_orders:
            await self._execute_orders_batch(limit_orders)
        
        # Update metrics
        execution_time = time.time() - start_time
        self.metrics["avg_execution_time"] = (
            (self.metrics["avg_execution_time"] * self.metrics["orders_placed"] + execution_time) /
            (self.metrics["orders_placed"] + len(batch))
        )
        self.metrics["orders_placed"] += len(batch)
        self.metrics["batch_efficiency"] = len(batch) / self.max_batch_size
        
        logger.debug(f"Processed batch of {len(batch)} orders in {execution_time:.3f}s")
    
    async def _execute_orders_batch(self, orders: List[OptimizedOrder]):
        """Execute a batch of similar orders"""
        tasks = []
        
        for order in orders:
            # Apply rate limiting
            await self._rate_limit()
            
            # Create execution task
            task = asyncio.create_task(self._execute_single_order(order))
            tasks.append(task)
        
        # Execute all orders concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for order, result in zip(orders, results):
            if isinstance(result, Exception):
                logger.error(f"Order {order.order_id} failed: {result}")
                order.status = OrderStatus.REJECTED
            else:
                logger.debug(f"Order {order.order_id} executed successfully")
    
    async def _execute_single_order(self, order: OptimizedOrder):
        """Execute a single order with the broker"""
        try:
            # Convert to broker format
            broker_order_data = self._convert_to_broker_format(order)
            
            # Execute with broker
            if hasattr(self.broker, 'place_order_async'):
                result = await self.broker.place_order_async(broker_order_data)
            else:
                # Fallback to sync execution in thread pool
                result = await asyncio.get_event_loop().run_in_executor(
                    self._executor,
                    self.broker.place_order,
                    broker_order_data
                )
            
            # Update order status
            if result.get("status") == "success":
                order.status = OrderStatus.SUBMITTED
                order.broker_order_id = result.get("order_id")
                self.metrics["orders_filled"] += 1
            else:
                order.status = OrderStatus.REJECTED
                logger.warning(f"Order rejected: {result.get('message', 'Unknown error')}")
            
            return result
            
        except Exception as e:
            order.status = OrderStatus.REJECTED
            logger.error(f"Error executing order {order.order_id}: {e}")
            raise
    
    async def _rate_limit(self):
        """Apply rate limiting to prevent API overload"""
        current_time = time.time()
        
        # Reset counter if a second has passed
        if current_time - self.rate_limiter["last_request_time"] >= 1.0:
            self.rate_limiter["request_count"] = 0
            self.rate_limiter["last_request_time"] = current_time
        
        # Check if we need to wait
        if self.rate_limiter["request_count"] >= self.rate_limiter["requests_per_second"]:
            wait_time = 1.0 - (current_time - self.rate_limiter["last_request_time"])
            if wait_time > 0:
                await asyncio.sleep(wait_time)
                self.rate_limiter["request_count"] = 0
                self.rate_limiter["last_request_time"] = time.time()
        
        self.rate_limiter["request_count"] += 1
    
    def _convert_to_broker_format(self, order: OptimizedOrder) -> Dict[str, Any]:
        """Convert optimized order to broker-specific format"""
        # This would be customized for each broker
        return {
            "symbol": order.symbol,
            "quantity": order.quantity,
            "side": order.side,
            "order_type": order.order_type.value,
            "price": order.price,
            "stop_price": order.stop_price
        }
    
    def _generate_order_id(self) -> str:
        """Generate unique order ID"""
        timestamp = int(time.time() * 1000000)  # microseconds
        return f"RT_{timestamp}"
    
    async def cancel_order_async(self, order_id: str) -> bool:
        """Cancel an order asynchronously"""
        with self.order_lock:
            order = self.orders.get(order_id)
            if not order:
                logger.warning(f"Order {order_id} not found")
                return False
            
            if order.status in [OrderStatus.FILLED, OrderStatus.CANCELLED]:
                logger.warning(f"Cannot cancel order {order_id} with status {order.status}")
                return False
        
        try:
            # Cancel with broker
            if hasattr(self.broker, 'cancel_order_async'):
                result = await self.broker.cancel_order_async(order.broker_order_id)
            else:
                result = await asyncio.get_event_loop().run_in_executor(
                    self._executor,
                    self.broker.cancel_order,
                    order.broker_order_id
                )
            
            if result.get("status") == "success":
                order.status = OrderStatus.CANCELLED
                self.metrics["orders_cancelled"] += 1
                logger.info(f"Order {order_id} cancelled successfully")
                return True
            else:
                logger.error(f"Failed to cancel order {order_id}: {result.get('message')}")
                return False
                
        except Exception as e:
            logger.error(f"Error cancelling order {order_id}: {e}")
            return False
    
    def get_order_status(self, order_id: str) -> Optional[OrderStatus]:
        """Get current order status"""
        with self.order_lock:
            order = self.orders.get(order_id)
            return order.status if order else None
    
    def get_orders_by_symbol(self, symbol: str) -> List[OptimizedOrder]:
        """Get all orders for a specific symbol"""
        with self.order_lock:
            return [order for order in self.orders.values() if order.symbol == symbol]
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        return {
            **self.metrics,
            "active_orders": len([o for o in self.orders.values() if o.status in [OrderStatus.PENDING, OrderStatus.SUBMITTED]]),
            "total_orders": len(self.orders),
            "queue_size": self.batch_queue.qsize() if hasattr(self.batch_queue, 'qsize') else 0
        }
