#!/usr/bin/env python3
"""
RapidTrader CLI - Command Line Interface for RapidTrader Trading Engine

This module provides a unified command-line interface to access all modules
of the RapidTrader trading engine.
"""

import os
import sys
import click
import logging

# Try to import rich, fallback to simple print if not available
try:
    from rich.console import Console
    from rich.table import Table
    HAS_RICH = True
    console = Console()
except ImportError:
    HAS_RICH = False
    console = None

import yaml

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("rapidtrader")

# Simple console functions for when rich is not available
def simple_print(text, style=None):
    """Simple print function that works without rich"""
    # Remove rich markup for simple output
    clean_text = text
    if '[bold' in clean_text:
        import re
        clean_text = re.sub(r'\[/?bold[^\]]*\]', '', clean_text)
        clean_text = re.sub(r'\[/?green[^\]]*\]', '', clean_text)
        clean_text = re.sub(r'\[/?red[^\]]*\]', '', clean_text)
        clean_text = re.sub(r'\[/?yellow[^\]]*\]', '', clean_text)
        clean_text = re.sub(r'\[/?blue[^\]]*\]', '', clean_text)
        clean_text = re.sub(r'\[/?magenta[^\]]*\]', '', clean_text)
    print(clean_text)

# Console wrapper that works with or without rich
class ConsoleWrapper:
    def print(self, text, style=None):
        if HAS_RICH and console:
            console.print(text, style=style)
        else:
            simple_print(text, style)

# Initialize console wrapper
console = ConsoleWrapper() if not HAS_RICH else console

# Constants
DEFAULT_CONFIG_PATH = "config/config.yml"
USER_DATA_DIR = "userdata"
STRATEGIES_DIR = f"{USER_DATA_DIR}/strategies"
CONFIGS_DIR = f"{USER_DATA_DIR}/configs"
CONFIG_TEMPLATE_PATH = f"{CONFIGS_DIR}/config_template.json"
HISTORICAL_DATA_DIR = f"{USER_DATA_DIR}/historical_data"
LOGS_DIR = f"{USER_DATA_DIR}/logs"
RESULTS_DIR = f"{USER_DATA_DIR}/results"

def setup_environment():
    """Set up the environment for RapidTrader."""
    # Add the project root directory to the Python path
    # Since this script is in the project root, use the directory containing this file
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = current_dir  # This file is in the project root

    # Verify this is a RapidTrader project directory by checking for key files
    if not (os.path.exists(os.path.join(project_root, "broker")) and
            os.path.exists(os.path.join(project_root, "core")) and
            os.path.exists(os.path.join(project_root, "data"))):
        # If not in project directory, try to find it
        # Look for common RapidTrader installation paths
        possible_paths = [
            "/home/<USER>/rapidtrader",
            os.path.expanduser("~/rapidtrader"),
            "/opt/rapidtrader",
            os.getcwd()  # Try current working directory
        ]

        for path in possible_paths:
            if (os.path.exists(path) and
                os.path.exists(os.path.join(path, "broker")) and
                os.path.exists(os.path.join(path, "core")) and
                os.path.exists(os.path.join(path, "data"))):
                project_root = path
                break
        else:
            # If we still can't find it, use current directory and hope for the best
            project_root = os.getcwd()

    sys.path.insert(0, project_root)

    # Create required directories
    os.makedirs(STRATEGIES_DIR, exist_ok=True)
    os.makedirs(CONFIGS_DIR, exist_ok=True)
    os.makedirs(HISTORICAL_DATA_DIR, exist_ok=True)
    os.makedirs(LOGS_DIR, exist_ok=True)
    os.makedirs(RESULTS_DIR, exist_ok=True)

    # Ensure base_strategy.py exists in userdata/strategies
    base_strategy_dst = os.path.join(STRATEGIES_DIR, "base_strategy.py")

    if not os.path.exists(base_strategy_dst):
        logger.debug(f"Base strategy already exists in {STRATEGIES_DIR}")

def load_config(config_path: str) -> dict:
    """
    Load configuration from a YAML file.

    Args:
        config_path: Path to the configuration file

    Returns:
        Configuration as a dictionary
    """
    try:
        with open(config_path, "r") as f:
            config = yaml.safe_load(f)
        return config or {}
    except FileNotFoundError:
        logger.warning(f"Configuration file not found: {config_path}")
        return {}
    except yaml.YAMLError as e:
        logger.error(f"Error parsing configuration file: {e}")
        return {}

@click.group()
@click.option(
    "--config", "-c",
    default=DEFAULT_CONFIG_PATH,
    help="Path to the configuration file",
)
@click.option(
    "--verbose", "-v",
    is_flag=True,
    help="Enable verbose output",
)
@click.pass_context
def cli(ctx, config, verbose):
    """RapidTrader - High-Performance Trading Engine"""
    # Set up the environment
    setup_environment()

    # Set up logging level
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Load configuration
    ctx.ensure_object(dict)
    ctx.obj["config"] = load_config(config)
    ctx.obj["config_path"] = config

    # Print welcome message
    if ctx.invoked_subcommand:
        console.print(f"[bold green]RapidTrader[/bold green] - Running command: [bold]{ctx.invoked_subcommand}[/bold]")

@cli.command()
@click.pass_context
def health(ctx):
    """Check system health and dependencies"""
    console.print("[bold green]🏥 RapidTrader System Health Check[/bold green]")

    health_status = check_system_health()

    # Create health table
    if HAS_RICH:
        health_table = Table(show_header=True, header_style="bold magenta")
        health_table.add_column("Component")
        health_table.add_column("Status")
        health_table.add_column("Details")

        # Python version check
        python_status = "✅ OK" if health_status['python_version'] else "❌ FAIL"
        python_details = f"Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        health_table.add_row("Python Version", python_status, python_details)

        # Required directories
        dirs_status = "✅ OK" if health_status['required_dirs'] else "❌ FAIL"
        dirs_details = "All required directories exist" if health_status['required_dirs'] else "Some directories missing"
        health_table.add_row("Required Directories", dirs_status, dirs_details)

        # Broker modules
        broker_status = "✅ OK" if health_status['broker_modules'] else "❌ FAIL"
        broker_details = "Broker modules available" if health_status['broker_modules'] else "Broker modules not found"
        health_table.add_row("Broker Modules", broker_status, broker_details)

        # Docker availability
        docker_status = "✅ OK" if health_status['docker_available'] else "⚠️ WARNING"
        docker_details = "Docker available" if health_status['docker_available'] else "Docker not available"
        health_table.add_row("Docker", docker_status, docker_details)

        console.print(health_table)
    else:
        # Simple table format without rich
        print("\nComponent               Status      Details")
        print("-" * 60)

        # Python version check
        python_status = "✅ OK" if health_status['python_version'] else "❌ FAIL"
        python_details = f"Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        print(f"{'Python Version':<20} {python_status:<10} {python_details}")

        # Required directories
        dirs_status = "✅ OK" if health_status['required_dirs'] else "❌ FAIL"
        dirs_details = "All required directories exist" if health_status['required_dirs'] else "Some directories missing"
        print(f"{'Required Directories':<20} {dirs_status:<10} {dirs_details}")

        # Broker modules
        broker_status = "✅ OK" if health_status['broker_modules'] else "❌ FAIL"
        broker_details = "Broker modules available" if health_status['broker_modules'] else "Broker modules not found"
        print(f"{'Broker Modules':<20} {broker_status:<10} {broker_details}")

        # Docker availability
        docker_status = "✅ OK" if health_status['docker_available'] else "⚠️ WARNING"
        docker_details = "Docker available" if health_status['docker_available'] else "Docker not available"
        print(f"{'Docker':<20} {docker_status:<10} {docker_details}")
        print()

    # Overall status
    all_critical_ok = health_status['python_version'] and health_status['required_dirs'] and health_status['broker_modules']

    if all_critical_ok:
        console.print("\n[bold green]✅ System is healthy and ready for trading![/bold green]")
    else:
        console.print("\n[bold red]❌ System has issues that need attention[/bold red]")
        console.print("Please resolve the issues above before using RapidTrader")

    # Additional recommendations
    console.print("\n[bold]Recommendations:[/bold]")
    if not health_status['docker_available']:
        console.print("• Install Docker for containerized trading")
    if not health_status['broker_modules']:
        console.print("• Check broker module installation")
    console.print("• Run 'rapidtrader create-config' to create configurations")
    console.print("• Run 'rapidtrader profile test --broker fyers' to test broker connection")

@cli.command(name="create-config")
@click.option(
    "--config", "-c",
    default=None,
    help="Name of the configuration file",
)
@click.option(
    "--exchange",
    default=None,
    help="Exchange name",
)
@click.option(
    "--dry-run/--live",
    default=None,
    help="Create configuration for dry-run (default) or live mode",
)
@click.option(
    "--interactive/--non-interactive",
    default=True,
    help="Use interactive mode to configure settings",
)
@click.pass_context
def create_config(ctx, config, exchange, dry_run, interactive):
    """Create a new configuration file"""
    import json

    # Interactive mode
    if interactive:
        console.print("[bold green]RapidTrader Configuration Creator[/bold green]")
        console.print("This command will create a new configuration file for RapidTrader.")
        console.print("Press Ctrl+C at any time to abort.\n")

        # Ask for configuration name if not provided
        if config is None:
            config = click.prompt("Configuration name", default="config_default")

        # Normalize config name
        if not config.endswith(".json"):
            config_name = f"{config}.json"
        else:
            config_name = config

        # Full path to the config file
        config_path = os.path.join(CONFIGS_DIR, config_name)

        # Check if config file already exists
        if os.path.exists(config_path):
            console.print(f"[bold yellow]Warning:[/bold yellow] Configuration file {config_name} already exists")
            if not click.confirm("Do you want to overwrite it?", default=False):
                return

        # Create configs directory if it doesn't exist
        os.makedirs(CONFIGS_DIR, exist_ok=True)

        # Load template
        try:
            with open(CONFIG_TEMPLATE_PATH, "r") as f:
                config_data = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            console.print(f"[bold red]Error:[/bold red] Could not load template: {e}")
            return

        # Ask for trading mode
        if dry_run is None:
            dry_run = click.confirm("Do you want to use dry-run mode?", default=True)

        # Ask for exchange
        available_exchanges = ["dhan", "zerodha", "upstox", "fyers"]
        if exchange is None:
            exchange = click.prompt(
                "Which exchange do you want to use?",
                type=click.Choice(available_exchanges),
                default="fyers"
            )

        # Ask for stake currency
        stake_currency = click.prompt("Stake currency", default="INR")

        # Ask for stake amount
        stake_amount = click.prompt("Stake amount per trade", default=1000, type=int)

        # Ask for max open trades
        max_open_trades = click.prompt("Maximum number of open trades", default=3, type=int)

        # Ask for strategies
        console.print("\n[bold]Available strategies:[/bold]")

        # Get strategies from userdata/strategies directory
        strategies = []
        if os.path.exists(STRATEGIES_DIR):
            strategies = [f[:-3] for f in os.listdir(STRATEGIES_DIR)
                         if f.endswith(".py") and not f.startswith("__") and f != "base_strategy"]

        if not strategies:
            console.print("[yellow]No strategies found. You can create one later with 'rapidtrader strategy create <name>'[/yellow]")
            selected_strategies = []
        else:
            console.print("Select strategies to include (space-separated numbers, empty to skip):")
            for i, strategy in enumerate(strategies):
                console.print(f"  {i+1}. {strategy}")

            selected_indices = click.prompt("Strategies to include", default="", show_default=False)

            if selected_indices:
                try:
                    indices = [int(idx.strip()) - 1 for idx in selected_indices.split()]
                    selected_strategies = [strategies[idx] for idx in indices if 0 <= idx < len(strategies)]
                except (ValueError, IndexError):
                    console.print("[bold red]Error:[/bold red] Invalid selection. No strategies will be included.")
                    selected_strategies = []
            else:
                selected_strategies = []

        # Ask for pairs
        default_pairs = ["SBIN-EQ", "RELIANCE-EQ", "INFY-EQ"]
        pairs_input = click.prompt(
            "Trading pairs (comma-separated)",
            default=",".join(default_pairs)
        )
        pairs = [p.strip() for p in pairs_input.split(",") if p.strip()]

        # Update configuration
        config_data["exchange"]["name"] = exchange
        config_data["broker"]["name"] = exchange
        config_data["dry_run"] = dry_run
        config_data["broker"]["dry_run"] = dry_run
        config_data["stake_currency"] = stake_currency
        config_data["stake_amount"] = stake_amount
        config_data["max_open_trades"] = max_open_trades

        # Update strategies
        config_data["strategies"] = []
        for strategy in selected_strategies:
            config_data["strategies"].append({
                "name": strategy,
                "enabled": True,
                "params": {}
            })

        # Update pairs
        if "pairlists" in config_data and config_data["pairlists"]:
            for pairlist in config_data["pairlists"]:
                if pairlist["method"] == "StaticPairList":
                    pairlist["config"]["pairs"] = pairs
                    break
    else:
        # Non-interactive mode
        # Normalize config name
        if config is None:
            config = "config_default"

        if not config.endswith(".json"):
            config_name = f"{config}.json"
        else:
            config_name = config

        # Full path to the config file
        config_path = os.path.join(CONFIGS_DIR, config_name)

        # Check if config file already exists
        if os.path.exists(config_path):
            console.print(f"[bold red]Error:[/bold red] Configuration file {config_name} already exists")
            if not click.confirm("Do you want to overwrite it?", default=False):
                return

        # Create configs directory if it doesn't exist
        os.makedirs(CONFIGS_DIR, exist_ok=True)

        # Load template
        try:
            with open(CONFIG_TEMPLATE_PATH, "r") as f:
                config_data = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            console.print(f"[bold red]Error:[/bold red] Could not load template: {e}")
            return

        # Set defaults if not provided
        if exchange is None:
            exchange = "dhan"

        if dry_run is None:
            dry_run = True

        # Update configuration
        config_data["exchange"]["name"] = exchange
        config_data["broker"]["name"] = exchange
        config_data["dry_run"] = dry_run
        config_data["broker"]["dry_run"] = dry_run

    # Write configuration
    try:
        with open(config_path, "w") as f:
            json.dump(config_data, f, indent=4)
        console.print(f"[bold green]Success:[/bold green] Configuration file created at {config_path}")
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] Could not write configuration file: {e}")
        return

    # Display configuration summary
    console.print("\n[bold]Configuration Summary:[/bold]")
    console.print(f"Exchange: {config_data['exchange']['name']}")
    console.print(f"Broker: {config_data['broker']['name']}")
    console.print(f"Mode: {'Dry Run' if config_data['dry_run'] else 'Live Trading'}")
    console.print(f"Stake Currency: {config_data['stake_currency']}")
    console.print(f"Stake Amount: {config_data['stake_amount']}")
    console.print(f"Max Open Trades: {config_data['max_open_trades']}")

    if "strategies" in config_data and config_data["strategies"]:
        console.print("\nStrategies:")
        for strategy in config_data["strategies"]:
            console.print(f"  - {strategy['name']} ({'Enabled' if strategy['enabled'] else 'Disabled'})")

    console.print("\n[bold]Next steps:[/bold]")
    console.print("1. Edit the configuration file to customize additional settings")
    if not config_data.get("strategies", []):
        console.print("2. Create a strategy: rapidtrader strategy create <name>")
    console.print(f"3. Run your strategy in {'dry-run' if config_data['dry_run'] else 'live'} mode: rapidtrader trade {'dryrun' if config_data['dry_run'] else 'start'} -c {config_name}")

# Trade commands
@cli.group()
def trade():
    """Trading commands"""
    pass

@trade.command()
@click.option(
    "--strategy", "-s",
    help="Strategy to use for trading (overrides config file)",
)
@click.option(
    "--config", "-c",
    required=True,
    help="Configuration file",
)
@click.option(
    "--strategy-list", "-sl",
    help="Comma-separated list of strategies to run (overrides config file)",
)
@click.pass_context
def start(ctx, strategy, config, strategy_list):
    """Start live trading with the specified strategy or strategies from config"""
    import json

    # Normalize config name
    if not config.endswith(".json"):
        config_name = f"{config}.json"
    else:
        config_name = config

    # Full path to the config file
    config_path = os.path.join(CONFIGS_DIR, config_name)

    # Check if config file exists
    if not os.path.exists(config_path):
        console.print(f"[bold red]Error:[/bold red] Configuration file {config_name} does not exist")
        return

    # Load configuration
    try:
        with open(config_path, "r") as f:
            config_data = json.load(f)
    except json.JSONDecodeError as e:
        console.print(f"[bold red]Error:[/bold red] Invalid JSON in configuration file: {e}")
        return

    # Check if it's a dry-run configuration
    if config_data.get("dry_run", False):
        console.print(f"[bold yellow]Warning:[/bold yellow] Configuration file {config_name} is set to dry-run mode")
        if not click.confirm("Do you want to continue with live trading?", default=False):
            console.print("Use 'rapidtrader trade dryrun' for dry-run mode")
            return

        # Update configuration to live mode
        config_data["dry_run"] = False
        config_data["broker"]["dry_run"] = False

        # Save updated configuration
        with open(config_path, "w") as f:
            json.dump(config_data, f, indent=4)
        console.print("[bold green]Configuration updated to live mode[/bold green]")

    # Determine which strategies to run
    strategies_to_run = []

    if strategy:
        # Single strategy specified via command line
        strategies_to_run = [{"name": strategy, "enabled": True, "params": {}}]
    elif strategy_list:
        # Multiple strategies specified via command line
        strategy_names = [s.strip() for s in strategy_list.split(",") if s.strip()]
        strategies_to_run = [{"name": name, "enabled": True, "params": {}} for name in strategy_names]
    elif "strategies" in config_data and config_data["strategies"]:
        # Use strategies from config file
        strategies_to_run = [s for s in config_data["strategies"] if s.get("enabled", True)]
    else:
        # Use legacy strategy field if present
        legacy_strategy = config_data.get("strategy", {}).get("name")
        if legacy_strategy:
            strategies_to_run = [{"name": legacy_strategy, "enabled": True, "params": config_data.get("strategy", {}).get("params", {})}]

    if not strategies_to_run:
        console.print("[bold red]Error:[/bold red] No strategies specified")
        return

    # Display strategies to run
    console.print(f"Starting live trading with {len(strategies_to_run)} strategies:")
    for s in strategies_to_run:
        console.print(f"  - [bold]{s['name']}[/bold]")

    # TODO: Implement live trading start logic
    # from core.live import start_live_trading
    # start_live_trading(strategies_to_run, config_path)

@trade.command()
@click.pass_context
def stop(ctx):
    """Stop all running trading sessions"""
    console.print("Stopping all trading sessions")
    # TODO: Implement trading stop logic
    # from core.live import stop_live_trading
    # stop_live_trading()

@trade.command()
@click.option(
    "--strategy", "-s",
    help="Strategy to use for dry run (overrides config file)",
)
@click.option(
    "--config", "-c",
    required=True,
    help="Configuration file",
)
@click.option(
    "--strategy-list", "-sl",
    help="Comma-separated list of strategies to run (overrides config file)",
)
@click.pass_context
def dryrun(ctx, strategy, config, strategy_list):
    """Run strategy in dry-run mode (no real trades)"""
    import json

    # Normalize config name
    if not config.endswith(".json"):
        config_name = f"{config}.json"
    else:
        config_name = config

    # Full path to the config file
    config_path = os.path.join(CONFIGS_DIR, config_name)

    # Check if config file exists
    if not os.path.exists(config_path):
        console.print(f"[bold red]Error:[/bold red] Configuration file {config_name} does not exist")
        return

    # Load configuration
    try:
        with open(config_path, "r") as f:
            config_data = json.load(f)
    except json.JSONDecodeError as e:
        console.print(f"[bold red]Error:[/bold red] Invalid JSON in configuration file: {e}")
        return

    # Check if it's a live configuration
    if not config_data.get("dry_run", True):
        console.print(f"[bold yellow]Warning:[/bold yellow] Configuration file {config_name} is set to live mode")
        if not click.confirm("Do you want to continue with dry-run mode?", default=False):
            console.print("Use 'rapidtrader trade start' for live trading")
            return

        # Update configuration to dry-run mode
        config_data["dry_run"] = True
        config_data["broker"]["dry_run"] = True

        # Save updated configuration
        with open(config_path, "w") as f:
            json.dump(config_data, f, indent=4)
        console.print("[bold green]Configuration updated to dry-run mode[/bold green]")

    # Determine which strategies to run
    strategies_to_run = []

    if strategy:
        # Single strategy specified via command line
        strategies_to_run = [{"name": strategy, "enabled": True, "params": {}}]
    elif strategy_list:
        # Multiple strategies specified via command line
        strategy_names = [s.strip() for s in strategy_list.split(",") if s.strip()]
        strategies_to_run = [{"name": name, "enabled": True, "params": {}} for name in strategy_names]
    elif "strategies" in config_data and config_data["strategies"]:
        # Use strategies from config file
        strategies_to_run = [s for s in config_data["strategies"] if s.get("enabled", True)]
    else:
        # Use legacy strategy field if present
        legacy_strategy = config_data.get("strategy", {}).get("name")
        if legacy_strategy:
            strategies_to_run = [{"name": legacy_strategy, "enabled": True, "params": config_data.get("strategy", {}).get("params", {})}]

    if not strategies_to_run:
        console.print("[bold red]Error:[/bold red] No strategies specified")
        return

    # Display strategies to run
    console.print(f"Starting dry run with {len(strategies_to_run)} strategies:")
    for s in strategies_to_run:
        console.print(f"  - [bold]{s['name']}[/bold]")

    # Start dry run engine
    try:
        # Add the project root to Python path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)

        from core.dryrun import start_dry_run
        start_dry_run(strategies_to_run, config_path)
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] Failed to start dry run: {e}")
        return

# Backtest commands
@cli.group()
def backtest():
    """Backtesting commands"""
    pass

@backtest.command()
@click.option(
    "--strategy", "-s",
    help="Strategy to backtest (overrides config file)",
)
@click.option(
    "--config", "-c",
    required=True,
    help="Configuration file",
)
@click.option(
    "--strategy-list", "-sl",
    help="Comma-separated list of strategies to backtest (overrides config file)",
)
@click.option(
    "--timerange",
    help="Time range for backtesting (format: YYYYMMDD-YYYYMMDD)",
)
@click.option(
    "--timeframe", "-t",
    help="Timeframe for backtesting (e.g., 1m, 5m, 1h, 1d)",
)
@click.option(
    "--optimized", "-o",
    is_flag=True,
    help="Use optimized backtesting engine for better performance",
)
@click.pass_context
def run(ctx, strategy, config, strategy_list, timerange, timeframe, optimized):
    """Run backtest for one or more strategies"""
    import json

    # Normalize config name
    if not config.endswith(".json"):
        config_name = f"{config}.json"
    else:
        config_name = config

    # Full path to the config file
    config_path = os.path.join(CONFIGS_DIR, config_name)

    # Check if config file exists
    if not os.path.exists(config_path):
        console.print(f"[bold red]Error:[/bold red] Configuration file {config_name} does not exist")
        return

    # Load configuration
    try:
        with open(config_path, "r") as f:
            config_data = json.load(f)
    except json.JSONDecodeError as e:
        console.print(f"[bold red]Error:[/bold red] Invalid JSON in configuration file: {e}")
        return

    # Determine which strategies to run
    strategies_to_run = []

    if strategy:
        # Single strategy specified via command line
        strategies_to_run = [{"name": strategy, "enabled": True, "params": {}}]
    elif strategy_list:
        # Multiple strategies specified via command line
        strategy_names = [s.strip() for s in strategy_list.split(",") if s.strip()]
        strategies_to_run = [{"name": name, "enabled": True, "params": {}} for name in strategy_names]
    elif "strategies" in config_data and config_data["strategies"]:
        # Use strategies from config file
        strategies_to_run = [s for s in config_data["strategies"] if s.get("enabled", True)]
    else:
        # Use legacy strategy field if present
        strategy_config = config_data.get("strategy")
        if strategy_config:
            if isinstance(strategy_config, str):
                # Strategy is just a string name
                strategies_to_run = [{"name": strategy_config, "enabled": True, "params": {}}]
            elif isinstance(strategy_config, dict):
                if strategy_config.get("name"):
                    # New format: strategy object with name and params
                    strategies_to_run = [{"name": strategy_config["name"], "enabled": True, "params": strategy_config.get("params", {})}]
                else:
                    # Old format: strategy object without name field
                    strategy_name = strategy_config.get("strategy_name", "DefaultStrategy")
                    strategies_to_run = [{"name": strategy_name, "enabled": True, "params": strategy_config.get("params", {})}]

    if not strategies_to_run:
        console.print("[bold red]Error:[/bold red] No strategies specified")
        return

    # Use timeframe from command line or config
    if not timeframe:
        timeframe = config_data.get("backtest", {}).get("timeframe") or config_data.get("timeframe", "1d")

    # Display strategies to run
    console.print(f"Running backtest for {len(strategies_to_run)} strategies:")
    for s in strategies_to_run:
        console.print(f"  - [bold]{s['name']}[/bold]")

    console.print(f"Timeframe: {timeframe}, Timerange: {timerange or 'all available data'}")

    if optimized:
        console.print("🚀 Using optimized backtesting engine for better performance")

    # Run backtest
    try:
        # Add the project root to Python path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)

        from core.backtest import run_backtest
        success = run_backtest(strategies_to_run, config_path, timerange, timeframe, optimized)
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] Failed to run backtest: {e}")
        return

    if success:
        console.print("\n[bold green]Backtest completed successfully![/bold green]")
    else:
        console.print("\n[bold red]Backtest failed![/bold red]")

@backtest.command(name="enhanced")
@click.option(
    "--strategy", "-s",
    required=True,
    help="Strategy name to use for backtesting",
)
@click.option(
    "--config", "-c",
    required=True,
    help="Configuration file name (without .json extension)",
)
@click.option(
    "--symbols", "-y",
    help="Comma-separated list of symbols (e.g., RELIANCE,TCS,INFY)",
)
@click.option(
    "--timeframe", "-t",
    default="1d",
    help="Data timeframe (e.g., 1m, 5m, 1h, 1d)",
)
@click.option(
    "--timerange", "-r",
    help="Time range in YYYYMMDD-YYYYMMDD format (e.g., 20240101-20240630)",
)
@click.option(
    "--no-update",
    is_flag=True,
    help="Skip automatic data update",
)
@click.pass_context
def enhanced_backtest(ctx, strategy, config, symbols, timeframe, timerange, no_update):
    """Run enhanced backtest with automatic Docker container deployment and data management"""
    try:
        import subprocess
        import os
        from datetime import datetime

        console.print(f"[bold green]🚀 Enhanced Backtest Runner[/bold green]")
        console.print(f"Strategy: [bold]{strategy}[/bold]")
        console.print(f"Config: [bold]{config}[/bold]")

        # Generate container name
        timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
        symbols_str = symbols.replace(',', '-') if symbols else "default"
        container_name = f"backtest-{symbols_str}-{config}-{strategy}-{timestamp}".lower()

        console.print(f"Container: [bold]{container_name}[/bold]")
        console.print("=" * 60)

        # Prepare environment variables
        env = os.environ.copy()
        env.update({
            "BACKTEST_CONTAINER_NAME": container_name,
            "STRATEGY": strategy,
            "CONFIG": config,
            "TIMEFRAME": timeframe,
        })

        if symbols:
            env["SYMBOLS"] = symbols
        if timerange:
            env["TIMERANGE"] = timerange

        # Run enhanced backtest using docker-compose
        console.print("[bold blue]📦 Deploying enhanced backtest container...[/bold blue]")

        cmd = ["docker-compose", "--profile", "enhanced-backtest", "up", "--remove-orphans"]

        with console.status("Running enhanced backtest..."):
            result = subprocess.run(cmd, env=env, cwd=".", capture_output=False)

        if result.returncode == 0:
            console.print("[bold green]✅ Enhanced backtest completed successfully![/bold green]")
            console.print(f"[bold]Container {container_name} has been automatically stopped[/bold]")

            # Show results location
            results_dir = "userdata/results"
            if os.path.exists(results_dir):
                console.print(f"[bold blue]📁 Results stored in:[/bold blue] {results_dir}")
        else:
            console.print("[bold red]❌ Enhanced backtest failed![/bold red]")

    except ImportError:
        console.print("[bold red]Error:[/bold red] Enhanced backtest requires additional dependencies")
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {e}")

@backtest.command()
@click.option(
    "--strategy", "-s",
    help="Strategy to show results for (default: all)",
)
@click.pass_context
def results(ctx, strategy):
    """Show backtest results"""
    if strategy:
        console.print(f"Showing backtest results for strategy: [bold]{strategy}[/bold]")
    else:
        console.print("Showing all backtest results")
    # Show backtest results
    try:
        # Add the project root to Python path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)

        from core.backtest import show_backtest_results
        show_backtest_results(strategy)
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] Failed to show backtest results: {e}")

# Strategy commands
@cli.group()
def strategy():
    """Strategy management commands"""
    pass

@strategy.command()
@click.pass_context
def list(ctx):
    """List all available strategies"""
    console.print("Available strategies:")

    # Get strategies from userdata/strategies directory
    if os.path.exists(STRATEGIES_DIR):
        strategies = [f[:-3] for f in os.listdir(STRATEGIES_DIR)
                     if f.endswith(".py") and not f.startswith("__") and f != "base_strategy"]
    else:
        strategies = []

    # Create a table to display strategies
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Strategy Name")
    table.add_column("Location")
    table.add_column("Config")

    for strategy_name in sorted(strategies):
        # Check if a config file exists for this strategy
        config_file = os.path.join(CONFIGS_DIR, f"{strategy_name}.json")
        config_status = "✅" if os.path.exists(config_file) else "❌"

        table.add_row(strategy_name, STRATEGIES_DIR, config_status)

    if not strategies:
        console.print("[yellow]No strategies found. Use 'rapidtrader strategy create <name>' to create one.[/yellow]")
    else:
        console.print(table)

@strategy.command()
@click.argument("name")
@click.option(
    "--config", "-c",
    is_flag=True,
    help="Create a configuration file for the strategy",
)
@click.pass_context
def create(ctx, name, config):
    """Create a new strategy"""
    console.print(f"Creating new strategy: [bold]{name}[/bold]")

    # Create strategies directory if it doesn't exist
    os.makedirs(STRATEGIES_DIR, exist_ok=True)

    # Create strategy file
    strategy_file = os.path.join(STRATEGIES_DIR, f"{name}.py")
    if os.path.exists(strategy_file):
        console.print(f"[bold red]Error:[/bold red] Strategy {name} already exists")
        return

    # Create strategy template
    strategy_template = f"""\"\"\"
{name} Strategy

This is a custom trading strategy for RapidTrader.
\"\"\"

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional

# Import the base strategy from userdata/strategies directory
try:
    from userdata.strategies.base_strategy import BaseStrategy
except ImportError:
    # BaseStrategy should exist in userdata/strategies
    raise ImportError("BaseStrategy not found in userdata/strategies. Please ensure base_strategy.py exists.")

class {name.capitalize()}Strategy(BaseStrategy):
    \"\"\"
    {name.capitalize()} trading strategy.
    \"\"\"

    def __init__(self, config=None):
        \"\"\"Initialize the strategy.\"\"\"
        super().__init__(config)
        self.name = "{name}"

        # Get parameters from config or use defaults
        strategy_params = self.config.get("strategy", {{}}).get("params", {{}})
        # TODO: Add your strategy parameters here
        # Example: self.param1 = strategy_params.get("param1", default_value)

    def populate_indicators(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        \"\"\"
        Add indicators to the dataframe.

        Args:
            dataframe: Price data

        Returns:
            Dataframe with indicators
        \"\"\"
        # TODO: Add your indicators here
        # Example: dataframe["sma_20"] = dataframe["close"].rolling(window=20).mean()
        return dataframe

    def populate_buy_signals(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        \"\"\"
        Generate buy signals.

        Args:
            dataframe: Price data with indicators

        Returns:
            Dataframe with buy signals
        \"\"\"
        # TODO: Add your buy signal logic here
        # Example: dataframe["buy"] = np.where(condition, 1, 0)
        return dataframe

    def populate_sell_signals(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        \"\"\"
        Generate sell signals.

        Args:
            dataframe: Price data with indicators

        Returns:
            Dataframe with sell signals
        \"\"\"
        # TODO: Add your sell signal logic here
        # Example: dataframe["sell"] = np.where(condition, 1, 0)
        return dataframe
"""

    with open(strategy_file, "w") as f:
        f.write(strategy_template)

    console.print(f"[bold green]Success:[/bold green] Strategy {name} created at {strategy_file}")

    # Create configuration file if requested
    if config:
        os.makedirs(CONFIGS_DIR, exist_ok=True)
        config_file = os.path.join(CONFIGS_DIR, f"{name}.json")

        if os.path.exists(config_file):
            console.print(f"[bold yellow]Warning:[/bold yellow] Configuration file for {name} already exists")
        else:
            import json

            # Create a default configuration
            default_config = {
                "strategy": {
                    "name": name,
                    "params": {
                        # Add default parameters here
                        "param1": 20,
                        "param2": 50
                    }
                },
                "trading": {
                    "max_open_trades": 3,
                    "stake_amount": 1000,
                    "stake_currency": "INR"
                }
            }

            with open(config_file, "w") as f:
                json.dump(default_config, f, indent=4)

            console.print(f"[bold green]Success:[/bold green] Configuration file created at {config_file}")

    console.print("\n[bold]Next steps:[/bold]")
    console.print("1. Edit your strategy in the created file")
    console.print(f"2. {'Edit' if config else 'Create'} a configuration file for your strategy")
    console.print("3. Test your strategy with backtesting: rapidtrader backtest run -s " + name)
    console.print("4. Run your strategy in dry-run mode: rapidtrader trade dryrun -s " + name)

@strategy.command()
@click.argument("name")
@click.option(
    "--params",
    help="Comma-separated list of parameters in format param1=value1,param2=value2",
)
@click.pass_context
def config(ctx, name, params):
    """Create or update a configuration file for a strategy"""
    # Check if the strategy exists
    strategy_file = os.path.join(STRATEGIES_DIR, f"{name}.py")
    if not os.path.exists(strategy_file):
        console.print(f"[bold red]Error:[/bold red] Strategy {name} does not exist")
        return

    # Create configs directory if it doesn't exist
    os.makedirs(CONFIGS_DIR, exist_ok=True)

    # Configuration file path
    config_file = os.path.join(CONFIGS_DIR, f"{name}.json")

    # Parse parameters if provided
    param_dict = {}
    if params:
        try:
            param_dict = {p.split("=")[0]: float(p.split("=")[1]) if p.split("=")[1].replace(".", "", 1).isdigit() else p.split("=")[1]
                          for p in params.split(",") if "=" in p}
        except Exception as e:
            console.print(f"[bold red]Error:[/bold red] Invalid parameter format: {e}")
            console.print("Parameters should be in format: param1=value1,param2=value2")
            return

    import json

    # Load existing config if it exists
    if os.path.exists(config_file):
        try:
            with open(config_file, "r") as f:
                config_data = json.load(f)
            console.print(f"Updating configuration for strategy: [bold]{name}[/bold]")
        except json.JSONDecodeError:
            console.print(f"[bold yellow]Warning:[/bold yellow] Invalid JSON in {config_file}, creating new configuration")
            config_data = {}
    else:
        console.print(f"Creating new configuration for strategy: [bold]{name}[/bold]")
        config_data = {}

    # Ensure the strategy section exists
    if "strategy" not in config_data:
        config_data["strategy"] = {}

    # Set the strategy name
    config_data["strategy"]["name"] = name

    # Ensure the params section exists
    if "params" not in config_data["strategy"]:
        config_data["strategy"]["params"] = {}

    # Update parameters if provided
    if param_dict:
        for key, value in param_dict.items():
            config_data["strategy"]["params"][key] = value

    # Ensure the trading section exists with defaults
    if "trading" not in config_data:
        config_data["trading"] = {
            "max_open_trades": 3,
            "stake_amount": 1000,
            "stake_currency": "INR"
        }

    # Write the configuration file
    with open(config_file, "w") as f:
        json.dump(config_data, f, indent=4)

    console.print(f"[bold green]Success:[/bold green] Configuration saved to {config_file}")

    # Display the configuration
    console.print("\n[bold]Configuration:[/bold]")
    console.print(json.dumps(config_data, indent=4))

# Data commands
@cli.group()
def data():
    """Data management commands"""
    pass

# Profile commands
@cli.group()
def profile():
    """User profile and account information commands"""
    pass

@profile.command()
@click.option(
    "--broker",
    default="fyers",
    help="Broker to get profile from (default: fyers)",
)
@click.pass_context
def show(ctx, broker):
    """Show user profile and account information"""
    try:
        if broker.lower() == "dhan":
            # Add the project root to Python path
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(current_dir)  # Go up one level from core/ to project root
            sys.path.insert(0, project_root)

            from broker.dhan_wrapper import DhanBroker

            # Initialize broker
            dhan_broker = DhanBroker()

            # Get enhanced user information
            with console.status("Fetching user profile..."):
                user_info = dhan_broker.get_enhanced_user_info()

            if not user_info or not user_info.get('user_profile'):
                console.print("[bold red]Error:[/bold red] Could not fetch user profile")
                console.print("Please check your broker credentials in .env file")
                return

            profile = user_info['user_profile']
            broker_info = user_info['broker_info']
            capabilities = user_info['trading_capabilities']

            # Display user profile
            console.print(f"\n[bold green]User Profile - {profile.get('dhanClientId', 'Unknown')}[/bold green]")

            # Basic Information Table
            basic_table = Table(show_header=True, header_style="bold magenta")
            basic_table.add_column("Field")
            basic_table.add_column("Value")

            basic_table.add_row("Client ID", profile.get('dhanClientId', 'N/A'))
            basic_table.add_row("Token Validity", profile.get('tokenValidity', 'N/A'))
            basic_table.add_row("Active Segments", profile.get('activeSegment', 'N/A'))
            basic_table.add_row("DDPI Status", profile.get('ddpi', 'N/A'))
            basic_table.add_row("MTF Status", profile.get('mtf', 'N/A'))
            basic_table.add_row("Data Plan", profile.get('dataPlan', 'N/A'))
            basic_table.add_row("Data Validity", profile.get('dataValidity', 'N/A'))

            console.print("\n[bold]Basic Information:[/bold]")
            console.print(basic_table)

            # Financial Information Table
            financial_table = Table(show_header=True, header_style="bold cyan")
            financial_table.add_column("Field")
            financial_table.add_column("Amount (INR)")

            financial_table.add_row("Available Balance", f"₹{broker_info.get('available_balance', 0):,.2f}")
            financial_table.add_row("Total Balance", f"₹{broker_info.get('total_balance', 0):,.2f}")
            financial_table.add_row("Used Margin", f"₹{broker_info.get('used_margin', 0):,.2f}")
            financial_table.add_row("Portfolio Value", f"₹{broker_info.get('portfolio_value', 0):,.2f}")
            financial_table.add_row("Current Risk", f"₹{broker_info.get('current_risk', 0):,.2f}")
            financial_table.add_row("Risk Percentage", f"{broker_info.get('risk_percentage', 0):.2f}%")

            console.print("\n[bold]Financial Information:[/bold]")
            console.print(financial_table)

            # Trading Capabilities Table
            capabilities_table = Table(show_header=True, header_style="bold yellow")
            capabilities_table.add_column("Capability")
            capabilities_table.add_column("Status")

            capabilities_table.add_row("Equity Trading", "[green]✓[/green]" if capabilities.get('can_trade_equity') else "[red]✗[/red]")
            capabilities_table.add_row("Derivatives Trading", "[green]✓[/green]" if capabilities.get('can_trade_derivatives') else "[red]✗[/red]")
            capabilities_table.add_row("Currency Trading", "[green]✓[/green]" if capabilities.get('can_trade_currency') else "[red]✗[/red]")
            capabilities_table.add_row("Commodity Trading", "[green]✓[/green]" if capabilities.get('can_trade_commodity') else "[red]✗[/red]")
            capabilities_table.add_row("DDPI Active", "[green]✓[/green]" if capabilities.get('ddpi_active') else "[red]✗[/red]")
            capabilities_table.add_row("MTF Active", "[green]✓[/green]" if capabilities.get('mtf_active') else "[red]✗[/red]")
            capabilities_table.add_row("Data Plan Active", "[green]✓[/green]" if capabilities.get('data_plan_active') else "[red]✗[/red]")

            console.print("\n[bold]Trading Capabilities:[/bold]")
            console.print(capabilities_table)

            # Risk Management Recommendations
            console.print("\n[bold]Risk Management Recommendations:[/bold]")
            available_balance = broker_info.get('available_balance', 0)
            if available_balance > 0:
                recommended_stake = min(available_balance * 0.02, 5000)  # 2% of balance or max 5000
                max_trades = max(1, int(available_balance * 0.1 / recommended_stake))  # 10% total risk

                console.print(f"• Recommended stake per trade: ₹{recommended_stake:,.0f}")
                console.print(f"• Maximum concurrent trades: {max_trades}")
                console.print(f"• Maximum portfolio risk: ₹{available_balance * 0.1:,.0f} (10%)")
            else:
                console.print("• [yellow]Warning:[/yellow] No available balance detected")

        elif broker.lower() == "fyers":
            # Add the project root to Python path
            current_dir = os.path.dirname(os.path.abspath(__file__))
            sys.path.insert(0, current_dir)

            from broker.fyers_wrapper import FyersBroker

            # Initialize broker
            fyers_broker = FyersBroker()

            # Get user profile
            with console.status("Fetching user profile..."):
                profile_response = fyers_broker.get_profile()

            if not profile_response or profile_response.get('s') != 'ok':
                console.print("[bold red]Error:[/bold red] Could not fetch user profile")
                console.print("Please check your Fyers credentials in .env file")
                return

            profile = profile_response.get('data', {})

            # Get funds information
            with console.status("Fetching funds information..."):
                funds_response = fyers_broker.get_funds()

            funds_data = {}
            if funds_response and funds_response.get('s') == 'ok':
                funds_data = funds_response.get('fund_limit', funds_response.get('data', {}))
                if isinstance(funds_data, list) and funds_data:
                    funds_data = funds_data[0]

            # Get positions
            with console.status("Fetching positions..."):
                positions_response = fyers_broker.get_positions()

            positions_data = []
            if positions_response and positions_response.get('s') == 'ok':
                positions_data = positions_response.get('netPositions', [])

            # Display user profile
            console.print(f"\n[bold green]Fyers User Profile - {profile.get('fy_id', 'Unknown')}[/bold green]")

            # Basic Information Table
            basic_table = Table(show_header=True, header_style="bold magenta")
            basic_table.add_column("Field")
            basic_table.add_column("Value")

            basic_table.add_row("User ID", profile.get('fy_id', 'N/A'))
            basic_table.add_row("Name", profile.get('name', 'N/A'))
            basic_table.add_row("Email", profile.get('email_id', 'N/A'))
            basic_table.add_row("PAN", profile.get('PAN', 'N/A'))
            basic_table.add_row("Mobile", profile.get('mobile_number', 'N/A'))
            basic_table.add_row("Display Name", profile.get('display_name', 'N/A'))

            console.print("\n[bold]Basic Information:[/bold]")
            console.print(basic_table)

            # Financial Information Table
            if funds_data:
                financial_table = Table(show_header=True, header_style="bold cyan")
                financial_table.add_column("Field")
                financial_table.add_column("Amount (INR)")

                available_cash = funds_data.get('availablecash', funds_data.get('available_cash', 0))
                utilized_margin = funds_data.get('utilizedmargin', funds_data.get('utilized_margin', 0))
                available_margin = funds_data.get('availablemargin', funds_data.get('available_margin', 0))

                financial_table.add_row("Available Cash", f"₹{float(available_cash):,.2f}")
                financial_table.add_row("Utilized Margin", f"₹{float(utilized_margin):,.2f}")
                financial_table.add_row("Available Margin", f"₹{float(available_margin):,.2f}")
                financial_table.add_row("Total Balance", f"₹{float(available_cash) + float(available_margin):,.2f}")

                console.print("\n[bold]Financial Information:[/bold]")
                console.print(financial_table)

            # Positions Information
            if positions_data:
                positions_table = Table(show_header=True, header_style="bold yellow")
                positions_table.add_column("Symbol")
                positions_table.add_column("Quantity")
                positions_table.add_column("P&L")
                positions_table.add_column("LTP")

                for pos in positions_data[:5]:  # Show first 5 positions
                    symbol = pos.get('symbol', 'N/A')
                    qty = pos.get('netQty', 0)
                    pnl = pos.get('realized_profit', pos.get('unrealized_profit', 0))
                    ltp = pos.get('ltp', 0)

                    positions_table.add_row(
                        symbol,
                        str(qty),
                        f"₹{float(pnl):,.2f}",
                        f"₹{float(ltp):,.2f}"
                    )

                console.print("\n[bold]Current Positions:[/bold]")
                console.print(positions_table)
                if len(positions_data) > 5:
                    console.print(f"... and {len(positions_data) - 5} more positions")
            else:
                console.print("\n[bold]Current Positions:[/bold] No open positions")

            # Risk Management Recommendations
            console.print("\n[bold]Risk Management Recommendations:[/bold]")
            if funds_data:
                available_balance = float(funds_data.get('availablecash', funds_data.get('available_cash', 0)))
                if available_balance > 0:
                    recommended_stake = min(available_balance * 0.02, 5000)  # 2% of balance or max 5000
                    max_trades = max(1, int(available_balance * 0.1 / recommended_stake))  # 10% total risk

                    console.print(f"• Recommended stake per trade: ₹{recommended_stake:,.0f}")
                    console.print(f"• Maximum concurrent trades: {max_trades}")
                    console.print(f"• Maximum portfolio risk: ₹{available_balance * 0.1:,.0f} (10%)")
                else:
                    console.print("• [yellow]Warning:[/yellow] No available balance detected")
            else:
                console.print("• [yellow]Warning:[/yellow] Could not fetch funds information")

        else:
            console.print(f"[bold red]Error:[/bold red] Unsupported broker: {broker}")
            console.print("Supported brokers: dhan, fyers")

    except ImportError as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Broker module not available. Make sure broker/dhan_wrapper.py exists.")
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Failed to fetch user profile. Please check your broker credentials.")

@profile.command()
@click.option(
    "--broker",
    default="fyers",
    help="Broker to test connection with (default: fyers)",
)
@click.pass_context
def test(ctx, broker):
    """Test broker connection and authentication"""
    try:
        if broker.lower() == "dhan":
            # Add the project root to Python path
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(current_dir)  # Go up one level from core/ to project root
            sys.path.insert(0, project_root)

            from broker.dhan_wrapper import DhanBroker

            console.print(f"Testing connection to {broker.upper()} broker...")

            # Initialize broker
            dhan_broker = DhanBroker()

            # Test basic profile fetch
            with console.status("Testing authentication..."):
                profile = dhan_broker.get_user_profile()

            if profile and profile.get('dhanClientId'):
                console.print(f"[bold green]✓ Connection successful![/bold green]")
                console.print(f"Connected as: {profile.get('dhanClientId')}")
                console.print(f"Token valid until: {profile.get('tokenValidity', 'Unknown')}")
            else:
                console.print("[bold red]✗ Connection failed![/bold red]")
                console.print("Please check your broker credentials in .env file")

        elif broker.lower() == "fyers":
            # Add the project root to Python path
            current_dir = os.path.dirname(os.path.abspath(__file__))
            sys.path.insert(0, current_dir)

            from broker.fyers_wrapper import FyersBroker

            console.print(f"Testing connection to {broker.upper()} broker...")

            # Initialize broker
            fyers_broker = FyersBroker()

            # Test basic profile fetch
            with console.status("Testing authentication..."):
                profile_response = fyers_broker.get_profile()

            if profile_response and profile_response.get('s') == 'ok':
                profile = profile_response.get('data', {})
                console.print(f"[bold green]✓ Connection successful![/bold green]")
                console.print(f"Connected as: {profile.get('fy_id', 'Unknown')}")
                console.print(f"Name: {profile.get('name', 'Unknown')}")
                console.print(f"Email: {profile.get('email_id', 'Unknown')}")

                # Test WebSocket connection
                console.print("\nTesting WebSocket connection...")
                if fyers_broker.enable_websocket():
                    console.print("[bold green]✓ WebSocket connection successful![/bold green]")
                    fyers_broker.disable_websocket()
                else:
                    console.print("[bold yellow]⚠ WebSocket connection failed[/bold yellow]")
            else:
                console.print("[bold red]✗ Connection failed![/bold red]")
                console.print("Please check your Fyers credentials in .env file")
                error_msg = profile_response.get('message', 'Unknown error') if profile_response else 'No response'
                console.print(f"Error: {error_msg}")

        else:
            console.print(f"[bold red]Error:[/bold red] Unsupported broker: {broker}")
            console.print("Supported brokers: dhan, fyers")

    except ImportError as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Broker module not available. Make sure broker/dhan_wrapper.py exists.")
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Failed to test broker connection. Please check your broker credentials.")

@profile.command()
@click.option(
    "--broker",
    default="dhan",
    help="Broker to check rate limits for (default: dhan)",
)
@click.pass_context
def rate_limits(ctx, broker):
    """Show current API rate limit status"""
    try:
        if broker.lower() == "dhan":
            # Add the project root to Python path
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(current_dir)  # Go up one level from core/ to project root
            sys.path.insert(0, project_root)

            from broker.dhan_wrapper import DhanBroker

            console.print(f"[bold]API Rate Limit Status - {broker.upper()}[/bold]\n")

            # Initialize broker to get rate limiter
            dhan_broker = DhanBroker()
            rate_limiter = dhan_broker.rate_limiter

            # Get rate limit status
            status = rate_limiter.get_rate_limit_status()

            if not status["enabled"]:
                console.print("[yellow]Rate limiting is disabled[/yellow]")
                return

            # Display rate limits for each endpoint type
            for endpoint_name, endpoint_data in status["endpoints"].items():
                console.print(f"[bold cyan]{endpoint_name.replace('_', ' ').title()}:[/bold cyan]")

                # Create table for this endpoint
                table = Table(show_header=True, header_style="bold magenta")
                table.add_column("Time Window")
                table.add_column("Limit")
                table.add_column("Used")
                table.add_column("Remaining")
                table.add_column("Usage %")
                table.add_column("Status")

                for time_window, data in endpoint_data.items():
                    limit = data["limit"]
                    current = data["current"]
                    remaining = data["remaining"]
                    percentage = data["percentage_used"]

                    # Color code based on usage
                    if percentage >= 90:
                        status_color = "[red]Critical[/red]"
                    elif percentage >= 75:
                        status_color = "[yellow]High[/yellow]"
                    elif percentage >= 50:
                        status_color = "[orange3]Medium[/orange3]"
                    else:
                        status_color = "[green]Low[/green]"

                    table.add_row(
                        time_window.title(),
                        str(limit),
                        str(current),
                        str(remaining),
                        f"{percentage:.1f}%",
                        status_color
                    )

                console.print(table)
                console.print()  # Add spacing

            # Show configuration
            console.print("[bold]Rate Limiting Configuration:[/bold]")
            config_table = Table(show_header=True, header_style="bold magenta")
            config_table.add_column("Setting")
            config_table.add_column("Value")

            config_table.add_row("Enabled", "✅ Yes" if rate_limiter.enabled else "❌ No")
            config_table.add_row("Strict Mode", "✅ Yes" if rate_limiter.strict_mode else "❌ No")
            config_table.add_row("Log Violations", "✅ Yes" if rate_limiter.log_violations else "❌ No")

            console.print(config_table)

        else:
            console.print(f"[bold red]Error:[/bold red] Unsupported broker: {broker}")
            console.print("Supported brokers: dhan")

    except ImportError as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Rate limiter module not available.")
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Failed to get rate limit status.")

@profile.command()
@click.option(
    "--broker",
    default="dhan",
    help="Broker to place test order with (default: dhan)",
)
@click.option(
    "--symbol",
    default="RELIANCE",
    help="Symbol to use for test order (default: RELIANCE)",
)
@click.option(
    "--quantity",
    type=int,
    default=1,
    help="Quantity for test order (default: 1)",
)
@click.option(
    "--price",
    type=float,
    default=2500.0,
    help="Price for test order (default: 2500.0)",
)
@click.pass_context
def test_order(ctx, broker, symbol, quantity, price):
    """Test order placement (dry-run mode only)"""
    try:
        if broker.lower() == "dhan":
            from broker.dhan_wrapper import DhanBroker, Exchange

            console.print(f"[bold]Testing Order Placement - {broker.upper()}[/bold]")
            console.print(f"Symbol: {symbol}, Quantity: {quantity}, Price: ₹{price}")

            # Initialize broker in dry-run mode
            dhan_broker = DhanBroker(dry_run=True)

            if not dhan_broker.dry_run:
                console.print("[bold red]Error:[/bold red] This command only works in dry-run mode")
                console.print("Set DRY_RUN_ENABLED=true in your .env file")
                return

            # Test order placement
            with console.status("Placing test order..."):
                response = dhan_broker.place_order(
                    symbol=symbol,
                    exchange=Exchange.NSE,
                    quantity=quantity,
                    side="BUY",
                    order_type="LIMIT",
                    price=price
                )

            if response.get("status") == "success":
                order_id = response.get("data", {}).get("order_id")
                console.print(f"[bold green]✓ Test order placed successfully![/bold green]")
                console.print(f"Order ID: {order_id}")

                # Show rate limit status after order
                console.print("\n[bold]Rate Limit Status After Order:[/bold]")
                rate_limiter = dhan_broker.rate_limiter
                status = rate_limiter.get_rate_limit_status()

                if status["enabled"]:
                    order_limits = status["endpoints"].get("order_placement", {})
                    for time_window, data in order_limits.items():
                        console.print(f"{time_window.title()}: {data['current']}/{data['limit']} ({data['percentage_used']:.1f}%)")

            else:
                console.print(f"[bold red]✗ Test order failed![/bold red]")
                console.print(f"Error: {response.get('message', 'Unknown error')}")

        else:
            console.print(f"[bold red]Error:[/bold red] Unsupported broker: {broker}")
            console.print("Supported brokers: dhan")

    except ImportError as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Broker module not available.")
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Failed to test order placement.")

@data.command()
@click.option(
    "--exchange",
    required=False,
    help="Exchange to download data from (nse, bse)",
)
@click.option(
    "--symbol",
    help="Single symbol to download (if not specified, downloads all symbols)",
)
@click.option(
    "--symbols",
    help="Comma-separated list of symbols to download",
)
@click.option(
    "--group",
    help="Symbol group to download (e.g., NIFTY50, BANKNIFTY)",
)
@click.option(
    "--list-groups",
    is_flag=True,
    help="List available symbol groups and exit",
)
@click.option(
    "--timeframe", "-t",
    default="1d",
    help="Timeframe (e.g., 1m, 5m, 15m, 30m, 1h, 1d, 1wk, 1mo, 3mo)",
)
@click.option(
    "--days",
    type=int,
    default=365,
    help="Number of days of history to download (used if start-date is not provided)",
)
@click.option(
    "--start-date",
    help="Start date (format: YYYY-MM-DD, YYYY/MM/DD, DD-MM-YYYY, DD/MM/YYYY, YYYYMMDD, DDMMYYYY)",
)
@click.option(
    "--end-date",
    help="End date (format: YYYY-MM-DD, YYYY/MM/DD, DD-MM-YYYY, DD/MM/YYYY, YYYYMMDD, DDMMYYYY)",
)
@click.option(
    "--max-workers",
    type=int,
    default=5,
    help="Maximum number of concurrent downloads",
)
@click.option(
    "--max-symbols",
    type=int,
    help="Maximum number of symbols to download",
)
@click.option(
    "--output-dir",
    default=f"{HISTORICAL_DATA_DIR}/yfinance",
    help="Directory to save the data",
)
@click.option(
    "--list-timeframes",
    is_flag=True,
    help="List available timeframes and exit",
)
@click.pass_context
def download(ctx, exchange, symbol, symbols, group, list_groups, timeframe, days, start_date, end_date, max_workers, max_symbols, output_dir, list_timeframes):
    """Download historical data using yfinance"""
    import subprocess

    # List timeframes and exit if requested
    if list_timeframes:
        console.print("[bold]Available Timeframes:[/bold]")
        console.print("Minutes: [bold]1m[/bold], [bold]2m[/bold], [bold]5m[/bold], [bold]15m[/bold], [bold]30m[/bold], [bold]60m[/bold], [bold]90m[/bold]")
        console.print("Hours: [bold]1h[/bold]")
        console.print("Days: [bold]1d[/bold]")
        console.print("Weeks: [bold]1wk[/bold]")
        console.print("Months: [bold]1mo[/bold]")
        console.print("Quarters: [bold]3mo[/bold]")
        console.print("")
        console.print("[bold yellow]Note:[/bold yellow] Minute and hour data is only available for the last 7 days")
        console.print("      Daily data is available for the last 50+ years")
        console.print("      Weekly and monthly data is available for the last 50+ years")
        return

    # List groups and exit if requested
    if list_groups:
        try:
            from data.group_manager import get_group_names, get_group_description, get_group_symbols, get_group_exchange

            group_names = get_group_names()

            if not group_names:
                console.print("[bold yellow]No symbol groups found[/bold yellow]")
                console.print("You can create symbol groups in data/symbols/groups.json")
                return

            console.print("[bold]Available Symbol Groups:[/bold]")

            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("Group")
            table.add_column("Exchange")
            table.add_column("Symbols")
            table.add_column("Description")

            for name in sorted(group_names):
                exchange = get_group_exchange(name) or "N/A"
                symbols = get_group_symbols(name)
                description = get_group_description(name) or ""

                table.add_row(
                    name,
                    exchange,
                    str(len(symbols)),
                    description
                )

            console.print(table)
            return
        except ImportError as e:
            console.print(f"[bold red]Error:[/bold red] {e}")
            console.print("Group manager not available. Make sure data/group_manager.py exists.")
            return

    # Handle group download
    if group:
        try:
            from data.group_manager import get_group_symbols, get_group_exchange

            # Get symbols for the group
            group_symbols = get_group_symbols(group)

            if not group_symbols:
                console.print(f"[bold red]Error:[/bold red] No symbols found for group {group}")
                console.print("Use --list-groups to see available groups")
                return

            # Get exchange for the group
            group_exchange = get_group_exchange(group)

            if not group_exchange:
                console.print(f"[bold yellow]Warning:[/bold yellow] No exchange specified for group {group}")
                if not exchange:
                    console.print("[bold red]Error:[/bold red] Exchange is required for downloading data")
                    console.print("Use --exchange NSE or --exchange BSE")
                    return
            else:
                # Use the group's exchange if not specified
                if not exchange:
                    exchange = group_exchange
                elif exchange.lower() != group_exchange.lower():
                    console.print(f"[bold yellow]Warning:[/bold yellow] Exchange mismatch: group {group} is for {group_exchange}, but {exchange} was specified")
                    console.print(f"Using {exchange} as specified")

            # Set symbols to the group symbols
            symbols = ",".join(group_symbols)
            console.print(f"Downloading data for group [bold]{group}[/bold] ({len(group_symbols)} symbols) from [bold]{exchange.upper()}[/bold]")
        except ImportError as e:
            console.print(f"[bold red]Error:[/bold red] {e}")
            console.print("Group manager not available. Make sure data/group_manager.py exists.")
            return

    # Validate exchange if provided
    if exchange and exchange.lower() not in ["nse", "bse"]:
        console.print(f"[bold red]Error:[/bold red] Unsupported exchange: {exchange}")
        console.print("Supported exchanges: NSE, BSE")
        return

    # Exchange is required if not just listing timeframes or groups
    if not list_timeframes and not list_groups and not exchange:
        console.print("[bold red]Error:[/bold red] Exchange is required for downloading data")
        console.print("Use --exchange NSE or --exchange BSE")
        return

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Build command arguments
    cmd_args = ["python", "data/download_data.py", "--exchange", exchange.lower()]

    if symbol:
        # Download a single symbol
        cmd_args.extend(["--symbol", symbol])
        console.print(f"Downloading data for symbol [bold]{symbol}[/bold] from [bold]{exchange.upper()}[/bold]")
    elif symbols:
        # Download specific symbols
        symbols_list = [s.strip() for s in symbols.split(",")]

        # Show progress
        console.print(f"Downloading data for [bold]{len(symbols_list)}[/bold] symbols from [bold]{exchange.upper()}[/bold]")

        # Create a progress table
        progress_table = Table(show_header=True, header_style="bold magenta")
        progress_table.add_column("Symbol")
        progress_table.add_column("Status")
        progress_table.add_column("Rows")

        success_count = 0
        total_rows = 0

        for i, s in enumerate(symbols_list):
            # Run the download_data.py script for each symbol
            symbol_cmd = cmd_args.copy()
            symbol_cmd.extend(["--symbol", s])
            symbol_cmd.extend(["--timeframe", timeframe, "--days", str(days)])

            # Add date range if provided
            if start_date:
                symbol_cmd.extend(["--start-date", start_date])
            if end_date:
                symbol_cmd.extend(["--end-date", end_date])

            console.print(f"[{i+1}/{len(symbols_list)}] Downloading data for symbol [bold]{s}[/bold]")
            result = subprocess.run(symbol_cmd, capture_output=True, text=True)

            # Check result
            if result.returncode == 0:
                # Try to extract row count
                rows = 0
                for line in result.stdout.strip().split("\n"):
                    if "INFO - Saved " in line and " rows to " in line:
                        try:
                            rows = int(line.split("INFO - Saved ")[1].split(" rows to ")[0])
                            break
                        except:
                            pass

                if rows > 0:
                    status = "[green]Success[/green]"
                    success_count += 1
                    total_rows += rows
                else:
                    status = "[yellow]No data[/yellow]"

                progress_table.add_row(s, status, str(rows))
            else:
                progress_table.add_row(s, "[red]Failed[/red]", "0")

        # Display summary
        console.print(progress_table)
        console.print(f"[bold green]Download completed for {len(symbols_list)} symbols[/bold green]")
        console.print(f"Success: {success_count}/{len(symbols_list)} ({success_count/len(symbols_list)*100:.1f}%)")
        console.print(f"Total rows: {total_rows}")
        return
    else:
        # Download all symbols with optional limit
        if max_symbols:
            cmd_args.extend(["--max-symbols", str(max_symbols)])
            console.print(f"Downloading data for up to [bold]{max_symbols}[/bold] symbols from [bold]{exchange.upper()}[/bold]")
        else:
            console.print(f"Downloading data for all symbols from [bold]{exchange.upper()}[/bold]")

    # Add common arguments
    cmd_args.extend(["--timeframe", timeframe, "--days", str(days), "--max-workers", str(max_workers), "--output-dir", output_dir])

    # Add date range if provided
    if start_date:
        cmd_args.extend(["--start-date", start_date])
        console.print(f"Start date: [bold]{start_date}[/bold]")

    if end_date:
        cmd_args.extend(["--end-date", end_date])
        console.print(f"End date: [bold]{end_date}[/bold]")

    # Run the download_data.py script
    console.print(f"Running command: {' '.join(cmd_args)}")
    with console.status("Downloading data..."):
        result = subprocess.run(cmd_args, capture_output=True, text=True)

    # Display results
    if result.returncode == 0:
        console.print("[bold green]Download completed successfully[/bold green]")

        # Parse the output to extract statistics
        output_lines = result.stdout.strip().split("\n")
        stats_lines = [line for line in output_lines if "INFO - " in line and any(x in line for x in ["Total symbols:", "Success count:", "Success rate:", "Total rows:", "Elapsed time:"])]

        if stats_lines:
            # Display statistics in a table
            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("Statistic")
            table.add_column("Value")

            for line in stats_lines:
                parts = line.split("INFO - ")[1].split(": ")
                if len(parts) == 2:
                    table.add_row(parts[0], parts[1])

            console.print(table)
    else:
        console.print("[bold red]Download failed[/bold red]")
        console.print(f"Error: {result.stderr}")

    # Suggest next steps
    console.print("\n[bold]Next steps:[/bold]")
    console.print("1. Use the downloaded data for backtesting: rapidtrader backtest run -c <config>")
    console.print("2. Run a strategy with the downloaded data: rapidtrader trade dryrun -c <config>")

@data.command(name="list-symbols")
@click.option(
    "--exchange",
    help="Exchange to list symbols for",
)
@click.option(
    "--source",
    default="yfinance",
    help="Data source to use",
)
@click.option(
    "--all",
    is_flag=True,
    help="List all available symbols for the exchange",
)
@click.option(
    "--limit",
    type=int,
    default=100,
    help="Limit the number of symbols to display",
)
@click.pass_context
def list_symbols(ctx, exchange, source, all, limit):
    """List available symbols"""
    from data.fetcher import Fetcher

    # Initialize fetcher
    fetcher = Fetcher()

    # Check if the source is available
    available_sources = fetcher.get_available_sources()
    if source not in available_sources:
        console.print(f"[bold red]Error:[/bold red] Data source {source} not available. Available sources: {available_sources}")
        return

    # Check if exchange is specified
    if not exchange:
        console.print(f"[bold yellow]Warning:[/bold yellow] No exchange specified. Please specify an exchange with --exchange.")
        console.print("Common exchanges:")
        console.print("  - [bold]NSE[/bold]: National Stock Exchange of India")
        console.print("  - [bold]BSE[/bold]: Bombay Stock Exchange")
        console.print("  - [bold]NYSE[/bold]: New York Stock Exchange")
        console.print("  - [bold]NASDAQ[/bold]: NASDAQ Stock Exchange")
        return

    # Get symbols
    if all:
        console.print(f"Fetching all available symbols for exchange: [bold]{exchange}[/bold]")
        with console.status("Fetching symbols..."):
            symbols = fetcher.get_all_symbols(exchange=exchange, source=source)
    else:
        symbols = fetcher.get_available_symbols(exchange=exchange, source=source)

    if not symbols:
        console.print(f"No symbols found for exchange: {exchange}")
        return

    # Display symbols
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Symbol")

    # Limit the number of symbols to display
    display_symbols = sorted(symbols)[:limit]

    for symbol in display_symbols:
        table.add_row(symbol)

    console.print(f"Found {len(symbols)} symbols for exchange: {exchange}")
    if len(symbols) > limit:
        console.print(f"Displaying first {limit} symbols. Use --limit to show more.")
    console.print(table)

@data.command(name="search-symbols")
@click.option(
    "--exchange",
    required=True,
    help="Exchange to search symbols in",
)
@click.option(
    "--query",
    required=True,
    help="Search query (case-insensitive)",
)
@click.option(
    "--source",
    default="yfinance",
    help="Data source to use",
)
@click.pass_context
def search_symbols(ctx, exchange, query, source):
    """Search for symbols in an exchange"""
    from data.fetcher import Fetcher

    # Initialize fetcher
    fetcher = Fetcher()

    # Check if the source is available
    available_sources = fetcher.get_available_sources()
    if source not in available_sources:
        console.print(f"[bold red]Error:[/bold red] Data source {source} not available. Available sources: {available_sources}")
        return

    # Get all symbols
    console.print(f"Searching for symbols matching '[bold]{query}[/bold]' in exchange: [bold]{exchange}[/bold]")
    with console.status("Fetching symbols..."):
        all_symbols = fetcher.get_all_symbols(exchange=exchange, source=source)

    # Filter symbols by query
    query = query.upper()
    matching_symbols = [symbol for symbol in all_symbols if query in symbol.upper()]

    if not matching_symbols:
        console.print(f"No symbols found matching '[bold]{query}[/bold]' in exchange: [bold]{exchange}[/bold]")
        return

    # Display matching symbols
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Symbol")

    for symbol in sorted(matching_symbols):
        table.add_row(symbol)

    console.print(f"Found {len(matching_symbols)} symbols matching '[bold]{query}[/bold]' in exchange: [bold]{exchange}[/bold]")
    console.print(table)

@data.command(name="list-timeframes")
@click.option(
    "--source",
    default="yfinance",
    help="Data source to use",
)
@click.pass_context
def list_timeframes(ctx, source):
    """List available timeframes"""
    from data.fetcher import Fetcher

    # Initialize fetcher
    fetcher = Fetcher()

    # Check if the source is available
    available_sources = fetcher.get_available_sources()
    if source not in available_sources:
        console.print(f"[bold red]Error:[/bold red] Data source {source} not available. Available sources: {available_sources}")
        return

    # Get timeframes
    timeframes = fetcher.get_available_timeframes(source=source)

    # Display timeframes
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Timeframe")
    table.add_column("Description")

    descriptions = {
        "1m": "1 minute",
        "2m": "2 minutes",
        "5m": "5 minutes",
        "15m": "15 minutes",
        "30m": "30 minutes",
        "1h": "1 hour",
        "1d": "1 day",
        "5d": "5 days",
        "1wk": "1 week",
        "1mo": "1 month",
        "3mo": "3 months"
    }

    for timeframe in timeframes:
        table.add_row(timeframe, descriptions.get(timeframe, ""))

    console.print(table)

@data.command(name="list-sources")
@click.pass_context
def list_sources(ctx):
    """List available data sources"""
    from data.fetcher import Fetcher

    # Initialize fetcher
    fetcher = Fetcher()

    # Get sources
    sources = fetcher.get_available_sources()

    # Display sources
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Source")
    table.add_column("Default")

    for source in sources:
        is_default = source == fetcher.default_source
        table.add_row(source, "[green]Yes[/green]" if is_default else "No")

    console.print(table)

@data.command(name="cleanup")
@click.option(
    "--days",
    type=int,
    default=365,
    help="Remove data older than this many days",
)
@click.option(
    "--exchange",
    help="Exchange to clean up (default: all)",
)
@click.option(
    "--symbol",
    help="Symbol to clean up (default: all)",
)
@click.option(
    "--timeframe",
    help="Timeframe to clean up (default: all)",
)
@click.option(
    "--dry-run/--execute",
    default=True,
    help="Simulate cleanup without deleting files (default: dry-run)",
)
@click.option(
    "--output-dir",
    default=f"{HISTORICAL_DATA_DIR}/yfinance",
    help="Directory containing data files",
)
@click.pass_context
def cleanup_data(ctx, days, exchange, symbol, timeframe, dry_run, output_dir):
    """Clean up old or unused data files"""
    try:
        from data.data_cleanup import cleanup_old_data, get_data_files, get_file_info

        # Get all matching files
        console.print(f"Scanning data directory: [bold]{output_dir}[/bold]")
        files = get_data_files(output_dir, exchange, symbol, timeframe)

        if not files:
            console.print("[bold yellow]No matching data files found[/bold yellow]")
            return

        console.print(f"Found [bold]{len(files)}[/bold] data files")

        # Confirm if not in dry-run mode
        if not dry_run:
            if not click.confirm("This will permanently delete data files. Continue?", default=False):
                console.print("[bold yellow]Operation cancelled[/bold yellow]")
                return

        # Perform cleanup
        with console.status("Cleaning up data..."):
            stats = cleanup_old_data(output_dir, days, dry_run)

        # Display results
        mode = "[bold yellow]DRY RUN[/bold yellow]" if dry_run else "[bold red]EXECUTED[/bold red]"
        console.print(f"Cleanup {mode} - would remove {stats['deleted_files']} of {stats['total_files']} files")
        console.print(f"Space that would be freed: {stats['freed_space'] / (1024*1024):.2f} MB")

        if dry_run and stats['deleted_files'] > 0:
            console.print("\n[bold]To execute the cleanup, run:[/bold]")
            console.print(f"rapidtrader data cleanup --days {days} --execute")

    except ImportError as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Data cleanup module not available. Make sure data/data_cleanup.py exists.")

@data.command(name="verify")
@click.option(
    "--exchange",
    help="Exchange to verify (default: all)",
)
@click.option(
    "--symbol",
    help="Symbol to verify (default: all)",
)
@click.option(
    "--timeframe",
    help="Timeframe to verify (default: all)",
)
@click.option(
    "--fix/--no-fix",
    default=False,
    help="Attempt to fix issues (default: no-fix)",
)
@click.option(
    "--output-dir",
    default=f"{HISTORICAL_DATA_DIR}/yfinance",
    help="Directory containing data files",
)
@click.pass_context
def verify_data(ctx, exchange, symbol, timeframe, fix, output_dir):
    """Verify data integrity and find issues"""
    try:
        from data.data_cleanup import verify_data_integrity, get_data_files

        # Get all matching files
        console.print(f"Scanning data directory: [bold]{output_dir}[/bold]")
        files = get_data_files(output_dir, exchange, symbol, timeframe)

        if not files:
            console.print("[bold yellow]No matching data files found[/bold yellow]")
            return

        console.print(f"Found [bold]{len(files)}[/bold] data files")

        # Confirm if fixing
        if fix:
            if not click.confirm("This will attempt to fix data issues. Continue?", default=False):
                console.print("[bold yellow]Operation cancelled[/bold yellow]")
                return

        # Perform verification
        with console.status("Verifying data integrity..."):
            stats = verify_data_integrity(output_dir, fix)

        # Display results
        console.print(f"Verification complete for {stats['total_files']} files")
        console.print(f"Corrupt files: {stats['corrupt_files']}")
        console.print(f"Files with gaps: {stats['files_with_gaps']}")
        console.print(f"Total gaps: {stats['total_gaps']}")

        if fix:
            console.print(f"Fixed files: {stats['fixed_files']}")
        elif stats['corrupt_files'] > 0 or stats['files_with_gaps'] > 0:
            console.print("\n[bold]To attempt fixing issues, run:[/bold]")
            console.print(f"rapidtrader data verify --fix")

    except ImportError as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Data verification module not available. Make sure data/data_cleanup.py exists.")

@data.command(name="summary")
@click.option(
    "--exchange",
    help="Exchange to summarize (default: all)",
)
@click.option(
    "--symbol",
    help="Symbol to summarize (default: all)",
)
@click.option(
    "--timeframe",
    help="Timeframe to summarize (default: all)",
)
@click.option(
    "--output-dir",
    default=f"{HISTORICAL_DATA_DIR}/yfinance",
    help="Directory containing data files",
)
@click.pass_context
def data_summary(ctx, exchange, symbol, timeframe, output_dir):
    """Show summary of available data"""
    try:
        from data.data_cleanup import get_data_files, get_file_info

        # Get all matching files
        console.print(f"Scanning data directory: [bold]{output_dir}[/bold]")
        files = get_data_files(output_dir, exchange, symbol, timeframe)

        if not files:
            console.print("[bold yellow]No matching data files found[/bold yellow]")
            return

        console.print(f"Found [bold]{len(files)}[/bold] data files")

        # Collect summary information
        summary = {}
        total_size = 0
        total_rows = 0

        with console.status("Analyzing data files..."):
            for file_path in files:
                info = get_file_info(file_path)

                # Extract exchange, symbol, timeframe from filename
                parts = os.path.basename(file_path).split("_")

                if len(parts) == 3:  # exchange_symbol_timeframe.csv
                    file_exchange, file_symbol, file_timeframe = parts[0], parts[1], parts[2].replace(".csv", "")
                elif len(parts) == 2:  # symbol_timeframe.csv
                    file_exchange, file_symbol, file_timeframe = "unknown", parts[0], parts[1].replace(".csv", "")
                else:
                    continue

                # Add to summary
                if file_exchange not in summary:
                    summary[file_exchange] = {}

                if file_symbol not in summary[file_exchange]:
                    summary[file_exchange][file_symbol] = {}

                summary[file_exchange][file_symbol][file_timeframe] = info

                # Update totals
                total_size += info["size"]
                total_rows += info["rows"]

        # Display summary
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Exchange")
        table.add_column("Symbol")
        table.add_column("Timeframe")
        table.add_column("Rows")
        table.add_column("Start Date")
        table.add_column("End Date")
        table.add_column("Size (KB)")
        table.add_column("Has Gaps")

        for ex in sorted(summary.keys()):
            for sym in sorted(summary[ex].keys()):
                for tf in sorted(summary[ex][sym].keys()):
                    info = summary[ex][sym][tf]

                    start_date = info["start_date"].strftime("%Y-%m-%d") if info["start_date"] else "N/A"
                    end_date = info["end_date"].strftime("%Y-%m-%d") if info["end_date"] else "N/A"

                    table.add_row(
                        ex,
                        sym,
                        tf,
                        str(info["rows"]),
                        start_date,
                        end_date,
                        f"{info['size'] / 1024:.1f}",
                        "[red]Yes[/red]" if info["has_gaps"] else "[green]No[/green]"
                    )

        console.print(table)
        console.print(f"Total files: {len(files)}")
        console.print(f"Total rows: {total_rows}")
        console.print(f"Total size: {total_size / (1024*1024):.2f} MB")

    except ImportError as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Data summary module not available. Make sure data/data_cleanup.py exists.")

@data.command(name="optimize")
@click.option(
    "--days",
    type=int,
    default=30,
    help="Compress files older than this many days",
)
@click.option(
    "--exchange",
    help="Exchange to optimize (default: all)",
)
@click.option(
    "--symbol",
    help="Symbol to optimize (default: all)",
)
@click.option(
    "--timeframe",
    help="Timeframe to optimize (default: all)",
)
@click.option(
    "--compress/--no-compress",
    default=True,
    help="Compress old data files (default: compress)",
)
@click.option(
    "--optimize-db/--no-optimize-db",
    default=True,
    help="Optimize database (default: optimize)",
)
@click.option(
    "--dry-run/--execute",
    default=True,
    help="Simulate optimization without modifying files (default: dry-run)",
)
@click.option(
    "--output-dir",
    default=f"{HISTORICAL_DATA_DIR}/yfinance",
    help="Directory containing data files",
)
@click.pass_context
def optimize_data(ctx, days, exchange, symbol, timeframe, compress, optimize_db, dry_run, output_dir):
    """Optimize data storage by compressing old files and optimizing database"""
    try:
        from data.data_optimizer import compress_old_data, optimize_database
        from data.data_cleanup import get_data_files

        # Get all matching files
        console.print(f"Scanning data directory: [bold]{output_dir}[/bold]")
        files = get_data_files(output_dir, exchange, symbol, timeframe)

        if not files:
            console.print("[bold yellow]No matching data files found[/bold yellow]")
            return

        console.print(f"Found [bold]{len(files)}[/bold] data files")

        # Confirm if not in dry-run mode
        if not dry_run:
            if not click.confirm("This will modify data files. Continue?", default=False):
                console.print("[bold yellow]Operation cancelled[/bold yellow]")
                return

        # Compress old data
        if compress:
            with console.status("Compressing old data..."):
                stats = compress_old_data(output_dir, days, dry_run)

            # Display compression results
            mode = "[bold yellow]DRY RUN[/bold yellow]" if dry_run else "[bold green]EXECUTED[/bold green]"
            console.print(f"Compression {mode} - would compress {stats['compressed_files']} of {stats['total_files']} files")
            console.print(f"Estimated space saved: {stats['space_saved'] / (1024*1024):.2f} MB")

            if stats['compressed_files'] > 0:
                console.print(f"Compression ratio: {stats['compression_ratio']:.1%}")

        # Optimize database
        if optimize_db:
            db_path = os.path.join(output_dir, "rapidtrader.db")

            if os.path.exists(db_path):
                with console.status("Optimizing database..."):
                    if not dry_run:
                        stats = optimize_database(db_path)

                        if stats["success"]:
                            console.print(f"[bold green]Database optimized:[/bold green] {db_path}")
                            console.print(f"Space saved: {stats['space_saved'] / 1024:.2f} KB")
                        else:
                            console.print(f"[bold red]Database optimization failed:[/bold red] {db_path}")
                    else:
                        console.print(f"[bold yellow]Would optimize database:[/bold yellow] {db_path}")
            else:
                console.print(f"[bold yellow]Database not found:[/bold yellow] {db_path}")

        # Show next steps
        if dry_run:
            console.print("\n[bold]To execute the optimization, run:[/bold]")
            console.print(f"rapidtrader data optimize --days {days} --execute")

    except ImportError as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Data optimizer module not available. Make sure data/data_optimizer.py exists.")

@data.group(name="symbols")
@click.pass_context
def symbols(ctx):
    """Manage exchange symbols"""
    pass

@symbols.command(name="refresh")
@click.option(
    "--exchange",
    help="Exchange to refresh symbols for (default: all)",
)
@click.pass_context
def refresh_symbols(ctx, exchange):
    """Refresh symbols from their sources"""
    try:
        # Try to use the new symbol_manager
        try:
            from data.symbol_manager import get_nse_symbols, get_bse_symbols, refresh_all

            with console.status("Refreshing symbols..."):
                if exchange:
                    # Refresh symbols for a specific exchange
                    if exchange.upper() == "NSE":
                        try:
                            # Try to import the NSE symbol fetcher directly
                            from data.nse_symbol_fetcher import refresh_symbols as refresh_nse
                            count = refresh_nse()
                            console.print(f"Refreshed [bold]{count}[/bold] NSE symbols")
                        except ImportError:
                            # Fall back to the symbol manager
                            symbols = get_nse_symbols(force_refresh=True)
                            console.print(f"Refreshed [bold]{len(symbols)}[/bold] NSE symbols")
                    elif exchange.upper() == "BSE":
                        try:
                            # Try to import the BSE symbol fetcher directly
                            from data.bse_symbol_fetcher import refresh_symbols as refresh_bse
                            count = refresh_bse()
                            console.print(f"Refreshed [bold]{count}[/bold] BSE symbols")
                        except ImportError:
                            # Fall back to the symbol manager
                            symbols = get_bse_symbols(force_refresh=True)
                            console.print(f"Refreshed [bold]{len(symbols)}[/bold] BSE symbols")
                    else:
                        console.print(f"[bold red]Error:[/bold red] Unsupported exchange: {exchange}")
                        console.print("Supported exchanges: NSE, BSE")
                else:
                    # Refresh symbols for all exchanges
                    result = refresh_all()

                    # Display results
                    table = Table(show_header=True, header_style="bold magenta")
                    table.add_column("Exchange")
                    table.add_column("Symbols")

                    for exchange, count in result.items():
                        if exchange != "total":
                            table.add_row(exchange.upper(), str(count))

                    table.add_row("TOTAL", str(result.get("total", 0)))

                    console.print("Symbol refresh completed:")
                    console.print(table)

            return
        except ImportError as e:
            # If the new symbol_manager is not available, try the individual fetchers
            console.print(f"[bold yellow]Warning:[/bold yellow] Symbol manager not available: {e}")

            if exchange:
                # Try to refresh a specific exchange
                if exchange.upper() == "NSE":
                    try:
                        from data.nse_symbol_fetcher import refresh_symbols as refresh_nse
                        count = refresh_nse()
                        console.print(f"Refreshed [bold]{count}[/bold] NSE symbols")
                        return
                    except ImportError:
                        console.print("[bold yellow]Warning:[/bold yellow] NSE symbol fetcher not available")
                elif exchange.upper() == "BSE":
                    try:
                        from data.bse_symbol_fetcher import refresh_symbols as refresh_bse
                        count = refresh_bse()
                        console.print(f"Refreshed [bold]{count}[/bold] BSE symbols")
                        return
                    except ImportError:
                        console.print("[bold yellow]Warning:[/bold yellow] BSE symbol fetcher not available")
            else:
                # Try to refresh all exchanges
                try:
                    from data.nse_symbol_fetcher import refresh_symbols as refresh_nse
                    from data.bse_symbol_fetcher import refresh_symbols as refresh_bse

                    nse_count = refresh_nse()
                    bse_count = refresh_bse()

                    # Display results
                    table = Table(show_header=True, header_style="bold magenta")
                    table.add_column("Exchange")
                    table.add_column("Symbols")

                    table.add_row("NSE", str(nse_count))
                    table.add_row("BSE", str(bse_count))
                    table.add_row("TOTAL", str(nse_count + bse_count))

                    console.print("Symbol refresh completed:")
                    console.print(table)
                    return
                except ImportError:
                    console.print("[bold yellow]Warning:[/bold yellow] Symbol fetchers not available")

        # Legacy method using core.symbol_manager
        try:
            from core.symbol_manager import SymbolManager

            # Initialize symbol manager
            symbol_manager = SymbolManager()

            with console.status("Refreshing symbols..."):
                if exchange:
                    # Refresh symbols for a specific exchange
                    if exchange.upper() == "NSE":
                        symbols = symbol_manager.get_nse_symbols(force_refresh=True)
                        console.print(f"Refreshed [bold]{len(symbols)}[/bold] NSE symbols")
                    elif exchange.upper() == "BSE":
                        symbols = symbol_manager.get_bse_symbols(force_refresh=True)
                        console.print(f"Refreshed [bold]{len(symbols)}[/bold] BSE symbols")
                    else:
                        console.print(f"[bold red]Error:[/bold red] Unsupported exchange: {exchange}")
                        console.print("Supported exchanges: NSE, BSE")
                else:
                    # Refresh symbols for all exchanges
                    result = symbol_manager.refresh_all()

                    # Display results
                    table = Table(show_header=True, header_style="bold magenta")
                    table.add_column("Exchange")
                    table.add_column("Symbols")

                    for exchange, count in result.items():
                        if exchange != "total":
                            table.add_row(exchange.upper(), str(count))

                    table.add_row("TOTAL", str(result.get("total", 0)))

                    console.print("Symbol refresh completed:")
                    console.print(table)
        except ImportError:
            console.print("[bold red]Error:[/bold red] No symbol manager available")
            console.print("Make sure either data.symbol_manager, data.nse_symbol_fetcher, data.bse_symbol_fetcher, or core.symbol_manager module is installed")
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Failed to refresh symbols")

@symbols.command(name="list")
@click.option(
    "--exchange",
    required=True,
    help="Exchange to list symbols for",
)
@click.option(
    "--limit",
    type=int,
    default=100,
    help="Limit the number of symbols to display",
)
@click.pass_context
def list_symbols_cmd(ctx, exchange, limit):
    """List symbols for an exchange"""
    try:
        # Try to use the new symbol_manager
        try:
            from data.symbol_manager import get_symbols

            with console.status(f"Loading symbols for {exchange.upper()}..."):
                symbols = get_symbols(exchange.lower())

                if not symbols:
                    console.print(f"[bold yellow]Warning:[/bold yellow] No symbols found for {exchange.upper()}")
                    console.print("Try refreshing the symbols with: rapidtrader data symbols refresh --exchange " + exchange.upper())
                    return

            # Display symbols
            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("Symbol")

            # Limit the number of symbols to display
            display_symbols = sorted(symbols)[:limit]

            for symbol in display_symbols:
                table.add_row(symbol)

            console.print(f"Found [bold]{len(symbols)}[/bold] symbols for exchange: [bold]{exchange.upper()}[/bold]")
            if len(symbols) > limit:
                console.print(f"Displaying first {limit} symbols. Use --limit to show more.")
            console.print(table)

            return
        except ImportError:
            # If the new symbol_manager is not available, try the individual fetchers
            if exchange.upper() == "NSE":
                try:
                    from data.nse_symbol_fetcher import get_symbols as get_nse_symbols

                    with console.status(f"Loading symbols for NSE..."):
                        symbols = get_nse_symbols()

                        if not symbols:
                            console.print("[bold yellow]Warning:[/bold yellow] No NSE symbols found")
                            console.print("Try refreshing the symbols with: rapidtrader data symbols refresh --exchange NSE")
                            return

                    # Display symbols
                    table = Table(show_header=True, header_style="bold magenta")
                    table.add_column("Symbol")

                    # Limit the number of symbols to display
                    display_symbols = sorted(symbols)[:limit]

                    for symbol in display_symbols:
                        table.add_row(symbol)

                    console.print(f"Found [bold]{len(symbols)}[/bold] symbols for exchange: [bold]NSE[/bold]")
                    if len(symbols) > limit:
                        console.print(f"Displaying first {limit} symbols. Use --limit to show more.")
                    console.print(table)

                    return
                except ImportError:
                    console.print("[bold yellow]Warning:[/bold yellow] NSE symbol fetcher not available")
            elif exchange.upper() == "BSE":
                try:
                    from data.bse_symbol_fetcher import get_symbols as get_bse_symbols

                    with console.status(f"Loading symbols for BSE..."):
                        symbols = get_bse_symbols()

                        if not symbols:
                            console.print("[bold yellow]Warning:[/bold yellow] No BSE symbols found")
                            console.print("Try refreshing the symbols with: rapidtrader data symbols refresh --exchange BSE")
                            return

                    # Display symbols
                    table = Table(show_header=True, header_style="bold magenta")
                    table.add_column("Symbol")

                    # Limit the number of symbols to display
                    display_symbols = sorted(symbols)[:limit]

                    for symbol in display_symbols:
                        table.add_row(symbol)

                    console.print(f"Found [bold]{len(symbols)}[/bold] symbols for exchange: [bold]BSE[/bold]")
                    if len(symbols) > limit:
                        console.print(f"Displaying first {limit} symbols. Use --limit to show more.")
                    console.print(table)

                    return
                except ImportError:
                    console.print("[bold yellow]Warning:[/bold yellow] BSE symbol fetcher not available")

        # Legacy method using core.symbol_manager
        try:
            from core.symbol_manager import SymbolManager

            # Initialize symbol manager
            symbol_manager = SymbolManager()

            with console.status(f"Loading symbols for {exchange.upper()}..."):
                if exchange.upper() == "NSE":
                    symbols = symbol_manager.get_nse_symbols()
                elif exchange.upper() == "BSE":
                    symbols = symbol_manager.get_bse_symbols()
                else:
                    console.print(f"[bold red]Error:[/bold red] Unsupported exchange: {exchange}")
                    console.print("Supported exchanges: NSE, BSE")
                    return

            # Display symbols
            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("Symbol")

            # Limit the number of symbols to display
            display_symbols = sorted(symbols)[:limit]

            for symbol in display_symbols:
                table.add_row(symbol)

            console.print(f"Found [bold]{len(symbols)}[/bold] symbols for exchange: [bold]{exchange.upper()}[/bold]")
            if len(symbols) > limit:
                console.print(f"Displaying first {limit} symbols. Use --limit to show more.")
            console.print(table)
            return
        except ImportError:
            pass

        # If we get here, no symbol manager is available
        console.print("[bold red]Error:[/bold red] No symbol manager available")
        console.print("Make sure either data.symbol_manager, data.nse_symbol_fetcher, data.bse_symbol_fetcher, or core.symbol_manager module is installed")
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Failed to list symbols")

@symbols.command(name="update")
@click.argument("csv_file", type=click.Path(exists=True))
@click.option(
    "--exchange",
    default="nse",
    help="Exchange to update symbols for (currently only NSE is supported)",
)
@click.option(
    "--symbol-column",
    default="SYMBOL",
    help="Name of the column containing symbols",
)
@click.option(
    "--series-column",
    help="Name of the column containing series",
)
@click.option(
    "--series-filter",
    help="Comma-separated list of series to include (e.g., EQ,BE)",
)
@click.option(
    "--merge",
    is_flag=True,
    help="Merge with existing symbols",
)
@click.pass_context
def update_symbols_cmd(ctx, csv_file, exchange, symbol_column, series_column, series_filter, merge):
    """Update symbols from a CSV file"""
    import subprocess

    if exchange.lower() != "nse":
        console.print(f"[bold red]Error:[/bold red] Only NSE exchange is currently supported for symbol updates")
        return

    # Build command arguments
    cmd_args = ["python", "update_nse_symbols.py", csv_file]

    if symbol_column:
        cmd_args.extend(["--symbol-column", symbol_column])

    if series_column:
        cmd_args.extend(["--series-column", series_column])

    if series_filter:
        cmd_args.extend(["--series-filter", series_filter])

    if merge:
        cmd_args.append("--merge")

    # Run the update_nse_symbols.py script
    console.print(f"Updating NSE symbols from [bold]{csv_file}[/bold]")
    console.print(f"Running command: {' '.join(cmd_args)}")

    with console.status("Updating symbols..."):
        result = subprocess.run(cmd_args, capture_output=True, text=True)

    # Display results
    if result.returncode == 0:
        console.print("[bold green]Symbols updated successfully[/bold green]")

        # Parse the output to extract statistics
        output_lines = result.stdout.strip().split("\n")
        info_lines = [line for line in output_lines if " - INFO - " in line]

        if info_lines:
            # Display statistics in a table
            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("Information")

            for line in info_lines:
                parts = line.split(" - INFO - ")[1]
                table.add_row(parts)

            console.print(table)
    else:
        console.print("[bold red]Symbol update failed[/bold red]")
        console.print(f"Error: {result.stderr}")

    # Suggest next steps
    console.print("\n[bold]Next steps:[/bold]")
    console.print("1. Download data with the updated symbols: rapidtrader data download --exchange NSE")
    console.print("2. View the updated symbols: rapidtrader data list-symbols --exchange NSE")

@symbols.command(name="search")
@click.option(
    "--exchange",
    required=True,
    help="Exchange to search symbols in",
)
@click.option(
    "--query",
    required=True,
    help="Search query (case-insensitive)",
)
@click.pass_context
def search_symbols_cmd(ctx, exchange, query):
    """Search for symbols in an exchange"""
    try:
        # Try to use the new symbol_manager
        try:
            from data.symbol_manager import get_symbols

            with console.status(f"Searching for symbols in {exchange.upper()}..."):
                all_symbols = get_symbols(exchange.lower())

                if not all_symbols:
                    console.print(f"[bold yellow]Warning:[/bold yellow] No symbols found for {exchange.upper()}")
                    console.print("Try refreshing the symbols with: rapidtrader data symbols refresh --exchange " + exchange.upper())
                    return

            # Filter symbols by query
            query = query.upper()
            matching_symbols = [symbol for symbol in all_symbols if query in symbol.upper()]

            # Display matching symbols
            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("Symbol")

            for symbol in sorted(matching_symbols):
                table.add_row(symbol)

            console.print(f"Found [bold]{len(matching_symbols)}[/bold] symbols matching '[bold]{query}[/bold]' in exchange: [bold]{exchange.upper()}[/bold]")
            console.print(table)
            return
        except ImportError:
            # If the new symbol_manager is not available, try the individual fetchers
            if exchange.upper() == "NSE":
                try:
                    from data.nse_symbol_fetcher import get_symbols as get_nse_symbols

                    with console.status(f"Searching for symbols in NSE..."):
                        all_symbols = get_nse_symbols()

                        if not all_symbols:
                            console.print("[bold yellow]Warning:[/bold yellow] No NSE symbols found")
                            console.print("Try refreshing the symbols with: rapidtrader data symbols refresh --exchange NSE")
                            return

                    # Filter symbols by query
                    query = query.upper()
                    matching_symbols = [symbol for symbol in all_symbols if query in symbol.upper()]

                    # Display matching symbols
                    table = Table(show_header=True, header_style="bold magenta")
                    table.add_column("Symbol")

                    for symbol in sorted(matching_symbols):
                        table.add_row(symbol)

                    console.print(f"Found [bold]{len(matching_symbols)}[/bold] symbols matching '[bold]{query}[/bold]' in exchange: [bold]NSE[/bold]")
                    console.print(table)
                    return
                except ImportError:
                    console.print("[bold yellow]Warning:[/bold yellow] NSE symbol fetcher not available")
            elif exchange.upper() == "BSE":
                try:
                    from data.bse_symbol_fetcher import get_symbols as get_bse_symbols

                    with console.status(f"Searching for symbols in BSE..."):
                        all_symbols = get_bse_symbols()

                        if not all_symbols:
                            console.print("[bold yellow]Warning:[/bold yellow] No BSE symbols found")
                            console.print("Try refreshing the symbols with: rapidtrader data symbols refresh --exchange BSE")
                            return

                    # Filter symbols by query
                    query = query.upper()
                    matching_symbols = [symbol for symbol in all_symbols if query in symbol.upper()]

                    # Display matching symbols
                    table = Table(show_header=True, header_style="bold magenta")
                    table.add_column("Symbol")

                    for symbol in sorted(matching_symbols):
                        table.add_row(symbol)

                    console.print(f"Found [bold]{len(matching_symbols)}[/bold] symbols matching '[bold]{query}[/bold]' in exchange: [bold]BSE[/bold]")
                    console.print(table)
                    return
                except ImportError:
                    console.print("[bold yellow]Warning:[/bold yellow] BSE symbol fetcher not available")

        # Legacy method using core.symbol_manager
        try:
            from core.symbol_manager import SymbolManager

            # Initialize symbol manager
            symbol_manager = SymbolManager()

            with console.status(f"Searching for symbols in {exchange.upper()}..."):
                if exchange.upper() == "NSE":
                    all_symbols = symbol_manager.get_nse_symbols()
                elif exchange.upper() == "BSE":
                    all_symbols = symbol_manager.get_bse_symbols()
                else:
                    console.print(f"[bold red]Error:[/bold red] Unsupported exchange: {exchange}")
                    console.print("Supported exchanges: NSE, BSE")
                    return

            # Filter symbols by query
            query = query.upper()
            matching_symbols = [symbol for symbol in all_symbols if query in symbol.upper()]

            # Display matching symbols
            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("Symbol")

            for symbol in sorted(matching_symbols):
                table.add_row(symbol)

            console.print(f"Found [bold]{len(matching_symbols)}[/bold] symbols matching '[bold]{query}[/bold]' in exchange: [bold]{exchange.upper()}[/bold]")
            console.print(table)
            return
        except ImportError:
            pass

        # If we get here, no symbol manager is available
        console.print("[bold red]Error:[/bold red] No symbol manager available")
        console.print("Make sure either data.symbol_manager, data.nse_symbol_fetcher, data.bse_symbol_fetcher, or core.symbol_manager module is installed")
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Failed to search symbols")

@symbols.command(name="scheduler")
@click.option(
    "--action",
    type=click.Choice(["start", "stop", "status", "run-now"]),
    required=True,
    help="Action to perform on the symbol scheduler",
)
@click.pass_context
def symbol_scheduler_cmd(ctx, action):
    """Manage the symbol scheduler"""
    try:
        from core.symbol_scheduler import get_scheduler

        # Get the scheduler
        scheduler = get_scheduler()

        if action == "start":
            # Start the scheduler
            scheduler.start()
            console.print("[green]Symbol scheduler started[/green]")
        elif action == "stop":
            # Stop the scheduler
            scheduler.stop()
            console.print("[yellow]Symbol scheduler stopped[/yellow]")
        elif action == "status":
            # Get scheduler status
            status = scheduler.get_status()

            # Display status
            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("Property")
            table.add_column("Value")

            table.add_row("Running", "[green]Yes[/green]" if status["running"] else "[red]No[/red]")
            table.add_row("Update Time", status["update_time"])
            table.add_row("Last Run", status["last_run"] or "Never")
            table.add_row("Next Run", status["next_run"] or "Not scheduled")

            console.print("Symbol scheduler status:")
            console.print(table)
        elif action == "run-now":
            # Run the scheduler now
            with console.status("Running symbol update..."):
                result = scheduler.run_now()

            if result["success"]:
                # Display results
                table = Table(show_header=True, header_style="bold magenta")
                table.add_column("Exchange")
                table.add_column("Symbols")

                for exchange, count in result["counts"].items():
                    if exchange != "total":
                        table.add_row(exchange.upper(), str(count))

                table.add_row("TOTAL", str(result["counts"].get("total", 0)))

                console.print("[green]Symbol update completed successfully[/green]")
                console.print(f"Timestamp: {result['timestamp']}")
                console.print(table)
            else:
                console.print(f"[bold red]Error:[/bold red] {result['error']}")
    except ImportError:
        console.print("[bold red]Error:[/bold red] Symbol scheduler not available")
        console.print("Make sure the core.symbol_scheduler module is installed")

# Money Management commands
@cli.group()
def money():
    """Money management commands"""
    pass

@money.command()
@click.pass_context
def status(ctx):
    """Show money management status and allocations"""
    try:
        # Add the project root to Python path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        sys.path.insert(0, project_root)

        from money_management import get_money_manager
        from broker.dhan_wrapper import DhanBroker

        # Initialize broker with live data enabled for money management
        broker = DhanBroker(live_data=True)
        money_manager = get_money_manager(broker=broker)

        with console.status("Fetching money management status..."):
            summary = money_manager.get_allocation_summary()

        if "error" in summary:
            console.print(f"[bold red]Error:[/bold red] {summary['error']}")
            return

        # Display balance information
        console.print("\n[bold green]💰 Money Management Status[/bold green]")

        balance_info = summary["balance_info"]
        balance_table = Table(show_header=True, header_style="bold cyan")
        balance_table.add_column("Balance Type")
        balance_table.add_column("Amount (INR)")
        balance_table.add_column("Percentage")

        total_balance = balance_info["total_balance"]
        balance_table.add_row("Total Balance", f"₹{total_balance:,.2f}", "100.0%")

        # Handle division by zero when total_balance is 0
        if total_balance > 0:
            available_pct = (balance_info['available_balance']/total_balance)*100
            used_margin_pct = (balance_info['used_margin']/total_balance)*100
            emergency_pct = (summary['emergency_reserve']/total_balance)*100
        else:
            available_pct = used_margin_pct = emergency_pct = 0.0

        balance_table.add_row("Available Balance", f"₹{balance_info['available_balance']:,.2f}",
                             f"{available_pct:.1f}%")
        balance_table.add_row("Used Margin", f"₹{balance_info['used_margin']:,.2f}",
                             f"{used_margin_pct:.1f}%")
        balance_table.add_row("Emergency Reserve", f"₹{summary['emergency_reserve']:,.2f}",
                             f"{emergency_pct:.1f}%")

        console.print("\n[bold]Account Balance:[/bold]")
        console.print(balance_table)

        # Display allocation summary
        allocation_table = Table(show_header=True, header_style="bold magenta")
        allocation_table.add_column("Metric")
        allocation_table.add_column("Value")

        allocation_table.add_row("Total Allocated", f"₹{summary['total_allocated_amount']:,.2f}")
        allocation_table.add_row("Allocated Percentage", f"{summary['total_allocated_percentage']:.1f}%")
        allocation_table.add_row("Unallocated Amount", f"₹{summary['unallocated_amount']:,.2f}")
        allocation_table.add_row("Unallocated Percentage", f"{summary['unallocated_percentage']:.1f}%")

        console.print("\n[bold]Allocation Summary:[/bold]")
        console.print(allocation_table)

        # Display strategy allocations
        if summary["strategies"]:
            strategy_table = Table(show_header=True, header_style="bold yellow")
            strategy_table.add_column("Strategy")
            strategy_table.add_column("Percentage")
            strategy_table.add_column("Amount (INR)")
            strategy_table.add_column("Max Risk")
            strategy_table.add_column("Max Positions")

            for strategy_name, allocation in summary["strategies"].items():
                strategy_table.add_row(
                    strategy_name,
                    f"{allocation['percentage']:.1f}%",
                    f"₹{allocation['amount']:,.2f}",
                    f"₹{allocation['max_risk_amount']:,.2f}",
                    str(allocation['max_positions'])
                )

            console.print("\n[bold]Strategy Allocations:[/bold]")
            console.print(strategy_table)
        else:
            console.print("\n[bold yellow]No strategy allocations configured[/bold yellow]")

        # Display risk limits
        risk_limits = summary["risk_limits"]
        console.print("\n[bold]Risk Management Limits:[/bold]")
        console.print(f"• Max Risk Per Trade: {risk_limits['max_risk_per_trade']}%")
        console.print(f"• Max Total Portfolio Risk: {risk_limits['max_total_risk']}%")
        console.print(f"• Global Stop Loss: {risk_limits['stop_loss_global']}%")

    except ImportError as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Money management module not available.")
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Failed to get money management status.")

@money.command()
@click.option("--strategy", "-s", required=True, help="Strategy name")
@click.option("--percentage", "-p", type=float, required=True, help="Allocation percentage (0-100)")
@click.pass_context
def allocate(ctx, strategy, percentage):
    """Allocate percentage of capital to a strategy"""
    try:
        # Add the project root to Python path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        sys.path.insert(0, project_root)

        from money_management import get_money_manager
        from broker.dhan_wrapper import DhanBroker

        # Initialize broker with live data enabled for money management
        broker = DhanBroker(live_data=True)
        money_manager = get_money_manager(broker=broker)

        console.print(f"Allocating {percentage}% to strategy: [bold]{strategy}[/bold]")

        success = money_manager.add_strategy_allocation(strategy, percentage)

        if success:
            console.print(f"[bold green]✓ Successfully allocated {percentage}% to {strategy}[/bold green]")

            # Show updated summary
            summary = money_manager.get_allocation_summary()
            if "strategies" in summary and strategy in summary["strategies"]:
                allocation = summary["strategies"][strategy]
                console.print(f"Allocated Amount: ₹{allocation['amount']:,.2f}")
                console.print(f"Max Risk Amount: ₹{allocation['max_risk_amount']:,.2f}")
        else:
            console.print(f"[bold red]✗ Failed to allocate {percentage}% to {strategy}[/bold red]")
            console.print("Check logs for details or verify percentage doesn't exceed limits.")

    except ImportError as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Money management module not available.")
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Failed to allocate strategy.")

@money.command()
@click.option("--strategy", "-s", required=True, help="Strategy name to remove")
@click.pass_context
def remove(ctx, strategy):
    """Remove strategy allocation"""
    try:
        # Add the project root to Python path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        sys.path.insert(0, project_root)

        from money_management import get_money_manager
        from broker.dhan_wrapper import DhanBroker

        # Initialize broker with live data enabled for money management
        broker = DhanBroker(live_data=True)
        money_manager = get_money_manager(broker=broker)

        if not click.confirm(f"Remove allocation for strategy '{strategy}'?", default=False):
            console.print("Operation cancelled.")
            return

        success = money_manager.remove_strategy_allocation(strategy)

        if success:
            console.print(f"[bold green]✓ Successfully removed allocation for {strategy}[/bold green]")
        else:
            console.print(f"[bold red]✗ Failed to remove allocation for {strategy}[/bold red]")
            console.print("Strategy may not exist in allocations.")

    except ImportError as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Money management module not available.")
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Failed to remove strategy allocation.")

@money.command()
@click.option("--total-capital-usage", type=float, help="Percentage of total capital to use (0-100)")
@click.option("--emergency-reserve", type=float, help="Emergency reserve percentage (0-100)")
@click.option("--max-risk-per-trade", type=float, help="Maximum risk per trade percentage (0-100)")
@click.option("--max-total-risk", type=float, help="Maximum total portfolio risk percentage (0-100)")
@click.option("--stop-loss-global", type=float, help="Global stop loss percentage (0-100)")
@click.option("--auto-rebalance/--no-auto-rebalance", help="Enable/disable auto rebalancing")
@click.pass_context
def configure(ctx, total_capital_usage, emergency_reserve, max_risk_per_trade,
              max_total_risk, stop_loss_global, auto_rebalance):
    """Configure global money management settings"""
    try:
        # Add the project root to Python path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        sys.path.insert(0, project_root)

        from money_management import get_money_manager
        from broker.dhan_wrapper import DhanBroker

        # Initialize broker with live data enabled for money management
        broker = DhanBroker(live_data=True)
        money_manager = get_money_manager(broker=broker)

        # Collect settings to update
        settings = {}

        if total_capital_usage is not None:
            if 0 <= total_capital_usage <= 100:
                settings["total_capital_usage"] = total_capital_usage
            else:
                console.print("[bold red]Error:[/bold red] Total capital usage must be between 0-100%")
                return

        if emergency_reserve is not None:
            if 0 <= emergency_reserve <= 100:
                settings["emergency_reserve"] = emergency_reserve
            else:
                console.print("[bold red]Error:[/bold red] Emergency reserve must be between 0-100%")
                return

        if max_risk_per_trade is not None:
            if 0 <= max_risk_per_trade <= 100:
                settings["max_risk_per_trade"] = max_risk_per_trade
            else:
                console.print("[bold red]Error:[/bold red] Max risk per trade must be between 0-100%")
                return

        if max_total_risk is not None:
            if 0 <= max_total_risk <= 100:
                settings["max_total_risk"] = max_total_risk
            else:
                console.print("[bold red]Error:[/bold red] Max total risk must be between 0-100%")
                return

        if stop_loss_global is not None:
            if 0 <= stop_loss_global <= 100:
                settings["stop_loss_global"] = stop_loss_global
            else:
                console.print("[bold red]Error:[/bold red] Global stop loss must be between 0-100%")
                return

        if auto_rebalance is not None:
            settings["auto_rebalance"] = auto_rebalance

        if not settings:
            console.print("[bold yellow]No settings provided to update[/bold yellow]")
            console.print("Use --help to see available options")
            return

        # Update settings
        success = money_manager.update_global_settings(settings)

        if success:
            console.print("[bold green]✓ Successfully updated money management settings[/bold green]")

            # Show updated settings
            for key, value in settings.items():
                if isinstance(value, bool):
                    console.print(f"• {key.replace('_', ' ').title()}: {'Enabled' if value else 'Disabled'}")
                else:
                    console.print(f"• {key.replace('_', ' ').title()}: {value}%")
        else:
            console.print("[bold red]✗ Failed to update settings[/bold red]")

    except ImportError as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Money management module not available.")
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Failed to configure money management.")

@money.command()
@click.option("--strategy", "-s", required=True, help="Strategy name")
@click.option("--symbol", required=True, help="Trading symbol")
@click.option("--price", "-p", type=float, required=True, help="Entry price")
@click.option("--risk-percentage", type=float, help="Custom risk percentage for this trade")
@click.pass_context
def position_size(ctx, strategy, symbol, price, risk_percentage):
    """Calculate position size for a trade"""
    try:
        # Add the project root to Python path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        sys.path.insert(0, project_root)

        from money_management import get_money_manager
        from broker.dhan_wrapper import DhanBroker

        # Initialize broker with live data enabled for money management
        broker = DhanBroker(live_data=True)
        money_manager = get_money_manager(broker=broker)

        console.print(f"Calculating position size for [bold]{symbol}[/bold] in strategy [bold]{strategy}[/bold]")

        with console.status("Calculating position size..."):
            position_info = money_manager.get_position_size(strategy, symbol, price, risk_percentage)

        if "error" in position_info:
            console.print(f"[bold red]Error:[/bold red] {position_info['error']}")
            return

        # Display position sizing information
        console.print("\n[bold green]📊 Position Sizing Calculation[/bold green]")

        sizing_table = Table(show_header=True, header_style="bold cyan")
        sizing_table.add_column("Parameter")
        sizing_table.add_column("Value")

        sizing_table.add_row("Symbol", position_info["symbol"])
        sizing_table.add_row("Strategy", position_info["strategy"])
        sizing_table.add_row("Entry Price", f"₹{position_info['price']:,.2f}")
        sizing_table.add_row("Recommended Quantity", str(position_info["quantity"]))
        sizing_table.add_row("Position Value", f"₹{position_info['position_value']:,.2f}")
        sizing_table.add_row("Risk Amount", f"₹{position_info['risk_amount']:,.2f}")
        sizing_table.add_row("Max Loss (5% SL)", f"₹{position_info['max_loss']:,.2f}")
        sizing_table.add_row("Allocation Used", f"{position_info['allocation_used']:.2f}%")

        console.print(sizing_table)

        # Show warnings if needed
        if position_info["allocation_used"] > 20:
            console.print("\n[bold yellow]⚠️  Warning:[/bold yellow] This position uses more than 20% of strategy allocation")

        if position_info["quantity"] == 0:
            console.print("\n[bold red]⚠️  Warning:[/bold red] Calculated quantity is 0. Price may be too high or allocation too low.")

    except ImportError as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Money management module not available.")
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Failed to calculate position size.")

@money.command()
@click.pass_context
def rebalance(ctx):
    """Check and perform portfolio rebalancing"""
    try:
        # Add the project root to Python path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        sys.path.insert(0, project_root)

        from money_management import get_money_manager
        from broker.dhan_wrapper import DhanBroker

        # Initialize broker with live data enabled for money management
        broker = DhanBroker(live_data=True)
        money_manager = get_money_manager(broker=broker)

        with console.status("Checking rebalancing requirements..."):
            rebalance_info = money_manager.rebalance_allocations()

        if "error" in rebalance_info:
            console.print(f"[bold red]Error:[/bold red] {rebalance_info['error']}")
            return

        console.print("\n[bold green]⚖️  Portfolio Rebalancing Analysis[/bold green]")

        if rebalance_info["required"]:
            console.print("[bold yellow]Rebalancing recommended[/bold yellow]")

            if rebalance_info["actions"]:
                console.print("\n[bold]Recommended Actions:[/bold]")
                for action in rebalance_info["actions"]:
                    console.print(f"• {action}")

            console.print(f"\nTotal Currently Allocated: ₹{rebalance_info['total_allocated']:,.2f}")

            if click.confirm("Would you like to proceed with rebalancing?", default=False):
                console.print("[bold green]Rebalancing would be implemented here[/bold green]")
                console.print("(This feature requires integration with portfolio tracking)")
            else:
                console.print("Rebalancing cancelled.")
        else:
            console.print("[bold green]✓ Portfolio is balanced - no rebalancing needed[/bold green]")
            console.print(f"Total Allocated: ₹{rebalance_info['total_allocated']:,.2f}")

    except ImportError as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Money management module not available.")
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        console.print("Failed to check rebalancing.")

# Initialize components at startup
def init_components():
    """Initialize RapidTrader components at startup."""
    try:
        # Check if we're in a Docker environment
        in_docker = os.path.exists('/.dockerenv')

        # Log startup information
        logger.info("🚀 RapidTrader CLI v2.0 - Starting up...")
        logger.info(f"Environment: {'Docker' if in_docker else 'Native'}")
        logger.info(f"Python version: {sys.version}")
        logger.info(f"Working directory: {os.getcwd()}")

        # Verify critical directories exist
        critical_dirs = [USER_DATA_DIR, STRATEGIES_DIR, CONFIGS_DIR, LOGS_DIR]
        for directory in critical_dirs:
            if not os.path.exists(directory):
                logger.warning(f"Creating missing directory: {directory}")
                os.makedirs(directory, exist_ok=True)

        # Check for broker modules
        broker_status = {}
        try:
            from broker.fyers_wrapper import FyersBroker
            broker_status['fyers'] = '✅ Available'
        except ImportError:
            broker_status['fyers'] = '❌ Not available'

        try:
            from broker.dhan_wrapper import DhanBroker
            broker_status['dhan'] = '✅ Available'
        except ImportError:
            broker_status['dhan'] = '❌ Not available'

        logger.info(f"Broker modules: {broker_status}")

        # Check for configuration files
        config_files = ['config_template.json', 'backtest-config.json', 'dryrun-config.json']
        missing_configs = []
        for config_file in config_files:
            config_path = os.path.join(CONFIGS_DIR, config_file)
            if not os.path.exists(config_path):
                missing_configs.append(config_file)

        if missing_configs:
            logger.warning(f"Missing configuration files: {missing_configs}")
            logger.info("Use 'rapidtrader create-config' to create configurations")

        logger.info("✅ RapidTrader initialization complete")

    except Exception as e:
        logger.error(f"Error during initialization: {e}")
        # Don't fail startup for initialization errors
        pass

def check_system_health():
    """Check system health and dependencies."""
    health_status = {
        'python_version': sys.version_info >= (3, 8),
        'required_dirs': all(os.path.exists(d) for d in [USER_DATA_DIR, STRATEGIES_DIR, CONFIGS_DIR]),
        'broker_modules': False,
        'docker_available': False
    }

    # Check broker modules
    try:
        import broker
        health_status['broker_modules'] = True
    except ImportError:
        pass

    # Check Docker availability
    try:
        import docker
        docker.from_env().ping()
        health_status['docker_available'] = True
    except:
        pass

    return health_status

if __name__ == "__main__":
    # Initialize components
    init_components()

# Independent Paper Trading commands
@cli.group(name="paper-trade")
def paper_trade():
    """Independent paper trading simulation using yfinance data"""
    pass

@paper_trade.command()
@click.option(
    "--config", "-c",
    default="userdata/config/dry_run_config.json",
    help="Configuration file path",
)
@click.option(
    "--capital",
    type=float,
    default=100000,
    help="Initial capital (default: 100000)",
)
@click.option(
    "--duration",
    type=int,
    default=0,
    help="Duration in seconds (0 = run indefinitely)",
)
@click.pass_context
def start(ctx, config, capital, duration):
    """Start independent paper trading simulation"""
    try:
        # Add the project root to Python path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)

        from core.independent_dry_run import IndependentDryRunEngine
        import signal
        import time

        console.print(f"[bold green]Starting Independent Paper Trading[/bold green]")
        console.print(f"Config: {config}")
        console.print(f"Initial Capital: ₹{capital:,.2f}")
        console.print(f"Duration: {'Indefinite' if duration == 0 else f'{duration} seconds'}")

        # Initialize engine
        engine = IndependentDryRunEngine(config, capital)

        # Start simulation
        engine.start_simulation()

        console.print("\n[bold]Paper trading started![/bold]")
        console.print("Press Ctrl+C to stop...")

        # Handle graceful shutdown
        def signal_handler(signum, frame):
            console.print("\n[yellow]Stopping paper trading...[/yellow]")
            engine.stop_simulation()

            # Show final summary
            summary = engine.get_portfolio_summary()
            console.print(f"\n[bold]Final Summary:[/bold]")
            console.print(f"Total Return: {summary['total_return']:.2f}%")
            console.print(f"Total Trades: {summary['total_trades']}")
            console.print(f"Win Rate: {summary['win_rate']:.1f}%")
            sys.exit(0)

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # Run for specified duration or indefinitely
        if duration > 0:
            time.sleep(duration)
            engine.stop_simulation()
        else:
            # Run indefinitely
            while engine.running:
                time.sleep(1)

    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {e}")

@paper_trade.command()
@click.pass_context
def stop(ctx):
    """Stop paper trading simulation"""
    console.print("Stopping paper trading simulation...")
    console.print("[yellow]Note: Use Ctrl+C to stop the running simulation[/yellow]")

@paper_trade.command()
@click.option(
    "--config", "-c",
    default="userdata/config/dry_run_config.json",
    help="Configuration file path",
)
@click.pass_context
def status(ctx, config):
    """Show paper trading status and portfolio summary"""
    try:
        from core.independent_dry_run import IndependentDryRunEngine

        # Create engine instance to get status
        engine = IndependentDryRunEngine(config, 100000)  # Dummy capital for status
        summary = engine.get_portfolio_summary()

        console.print(f"[bold]Paper Trading Status[/bold]")

        # Portfolio Summary Table
        summary_table = Table(show_header=True, header_style="bold magenta")
        summary_table.add_column("Metric")
        summary_table.add_column("Value")

        summary_table.add_row("Initial Capital", f"₹{summary['initial_capital']:,.2f}")
        summary_table.add_row("Current Value", f"₹{summary['current_value']:,.2f}")
        summary_table.add_row("Available Balance", f"₹{summary['available_balance']:,.2f}")
        summary_table.add_row("Total Return", f"{summary['total_return']:.2f}%")
        summary_table.add_row("Realized P&L", f"₹{summary['realized_pnl']:,.2f}")
        summary_table.add_row("Unrealized P&L", f"₹{summary['unrealized_pnl']:,.2f}")
        summary_table.add_row("Total Trades", str(summary['total_trades']))
        summary_table.add_row("Winning Trades", str(summary['winning_trades']))
        summary_table.add_row("Win Rate", f"{summary['win_rate']:.1f}%")
        summary_table.add_row("Max Drawdown", f"{summary['max_drawdown']:.2f}%")
        summary_table.add_row("Open Positions", str(summary['open_positions']))

        console.print(summary_table)

    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {e}")

if __name__ == "__main__":
    # Run the CLI
    cli(obj={})
