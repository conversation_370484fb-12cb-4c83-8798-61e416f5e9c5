#!/usr/bin/env python3
"""
Broker-Specific Symbol Manager for RapidTrader

This module provides comprehensive broker-specific symbol management including:
- Broker-specific symbol downloads and storage
- Automatic symbol updates when new broker configs are created
- Unified symbol mapping across brokers
- Master contract management per broker
- Integration with Fyers, DhanHQ, and other brokers
- OpenAlgo-style symbol management
"""

import os
import sys
import json
import logging
import requests
import pandas as pd
import schedule
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
from concurrent.futures import ThreadPoolExecutor, as_completed

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("broker_symbol_manager")

class BrokerSymbolManager:
    """Broker-Specific Symbol Manager for RapidTrader"""

    def __init__(self, data_dir: str = "data/symbols"):
        """Initialize the Broker Symbol Manager"""
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)

        # Create broker-specific directories
        self.brokers_dir = self.data_dir / "brokers"
        self.brokers_dir.mkdir(parents=True, exist_ok=True)

        # Core symbol files
        self.master_contract_file = self.data_dir / "master_contract.json"
        self.unified_mapping_file = self.data_dir / "unified_symbol_mapping.json"
        self.broker_configs_file = self.data_dir / "broker_configs.json"

        # Cache
        self._symbol_cache = {}
        self._broker_cache = {}
        self._last_update = {}

        # Configuration
        self.update_interval_hours = 24  # Update daily
        self.max_retries = 3
        self.timeout = 30

        # Supported brokers and their downloaders
        self.supported_brokers = {
            "fyers": self._download_fyers_symbols,
            "dhan": self._download_dhan_symbols,
            "zerodha": self._download_zerodha_symbols,
            "upstox": self._download_upstox_symbols,
            "angel": self._download_angel_symbols,
            "iifl": self._download_iifl_symbols
        }

        # Load existing configurations
        self._load_broker_configs()

        logger.info("Broker Symbol Manager initialized")

    def _load_broker_configs(self):
        """Load existing broker configurations"""
        try:
            if self.broker_configs_file.exists():
                with open(self.broker_configs_file, 'r') as f:
                    self.broker_configs = json.load(f)
            else:
                self.broker_configs = {}
                self._save_broker_configs()
        except Exception as e:
            logger.error(f"Error loading broker configs: {e}")
            self.broker_configs = {}

    def _save_broker_configs(self):
        """Save broker configurations"""
        try:
            with open(self.broker_configs_file, 'w') as f:
                json.dump(self.broker_configs, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving broker configs: {e}")

    def register_broker_config(self, broker_name: str, config: Dict[str, Any]) -> bool:
        """
        Register a new broker configuration and trigger symbol download

        Args:
            broker_name: Name of the broker (e.g., 'fyers', 'dhan')
            config: Broker configuration dictionary

        Returns:
            True if successful, False otherwise
        """
        try:
            # Normalize broker name
            broker_name = broker_name.lower()

            # Store configuration
            self.broker_configs[broker_name] = {
                "config": config,
                "registered_at": datetime.now().isoformat(),
                "last_symbol_update": None,
                "symbol_count": 0,
                "status": "registered"
            }

            # Save configurations
            self._save_broker_configs()

            # Trigger symbol download for this broker
            logger.info(f"Registered broker {broker_name}, triggering symbol download...")
            success = self.update_broker_symbols(broker_name)

            if success:
                self.broker_configs[broker_name]["status"] = "active"
                self._save_broker_configs()
                logger.info(f"Successfully registered and updated symbols for broker: {broker_name}")
            else:
                self.broker_configs[broker_name]["status"] = "error"
                self._save_broker_configs()
                logger.warning(f"Broker {broker_name} registered but symbol download failed")

            return success

        except Exception as e:
            logger.error(f"Error registering broker {broker_name}: {e}")
            return False

    def update_broker_symbols(self, broker_name: str) -> bool:
        """
        Update symbols for a specific broker

        Args:
            broker_name: Name of the broker

        Returns:
            True if successful, False otherwise
        """
        try:
            broker_name = broker_name.lower()

            if broker_name not in self.supported_brokers:
                logger.error(f"Unsupported broker: {broker_name}")
                return False

            # Get broker configuration
            broker_config = self.broker_configs.get(broker_name, {}).get("config", {})

            # Download symbols using broker-specific method
            downloader = self.supported_brokers[broker_name]
            symbols = downloader(broker_config)

            if symbols:
                # Save broker-specific symbols
                success = self._save_broker_symbols(broker_name, symbols)

                if success:
                    # Update broker config
                    if broker_name in self.broker_configs:
                        self.broker_configs[broker_name]["last_symbol_update"] = datetime.now().isoformat()
                        self.broker_configs[broker_name]["symbol_count"] = len(symbols)
                        self._save_broker_configs()

                    # Update unified mapping
                    self._update_unified_mapping(broker_name, symbols)

                    logger.info(f"Updated {len(symbols)} symbols for broker: {broker_name}")
                    return True
                else:
                    logger.error(f"Failed to save symbols for broker: {broker_name}")
                    return False
            else:
                logger.warning(f"No symbols downloaded for broker: {broker_name}")
                return False

        except Exception as e:
            logger.error(f"Error updating symbols for broker {broker_name}: {e}")
            return False

    def _save_broker_symbols(self, broker_name: str, symbols: List[Dict[str, Any]]) -> bool:
        """Save symbols for a specific broker"""
        try:
            broker_dir = self.brokers_dir / broker_name
            broker_dir.mkdir(parents=True, exist_ok=True)

            # Create broker symbol file
            symbol_file = broker_dir / f"{broker_name}_symbols.json"

            # Create backup if file exists
            if symbol_file.exists():
                backup_file = broker_dir / f"{broker_name}_symbols_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                symbol_file.rename(backup_file)
                logger.info(f"Created backup: {backup_file}")

            # Save symbols
            data = {
                "broker": broker_name,
                "symbols": symbols,
                "count": len(symbols),
                "updated_at": datetime.now().isoformat(),
                "version": "1.0"
            }

            with open(symbol_file, 'w') as f:
                json.dump(data, f, indent=2)

            # Also save as CSV for easy viewing
            csv_file = broker_dir / f"{broker_name}_symbols.csv"
            try:
                df = pd.DataFrame(symbols)
                df.to_csv(csv_file, index=False)
            except Exception as e:
                logger.debug(f"Could not save CSV for {broker_name}: {e}")

            logger.info(f"Saved {len(symbols)} symbols for {broker_name}")
            return True

        except Exception as e:
            logger.error(f"Error saving symbols for {broker_name}: {e}")
            return False

    def _update_unified_mapping(self, broker_name: str, symbols: List[Dict[str, Any]]):
        """Update unified symbol mapping with broker-specific symbols"""
        try:
            # Load existing unified mapping
            unified_mapping = {}
            if self.unified_mapping_file.exists():
                with open(self.unified_mapping_file, 'r') as f:
                    unified_mapping = json.load(f)

            # Initialize structure if needed
            if "brokers" not in unified_mapping:
                unified_mapping["brokers"] = {}
            if "unified_symbols" not in unified_mapping:
                unified_mapping["unified_symbols"] = {}

            # Add broker symbols
            unified_mapping["brokers"][broker_name] = {
                "symbols": symbols,
                "count": len(symbols),
                "last_updated": datetime.now().isoformat()
            }

            # Create unified symbol mapping
            for symbol_data in symbols:
                rapidtrader_symbol = symbol_data.get("symbol", "").upper()
                if rapidtrader_symbol:
                    if rapidtrader_symbol not in unified_mapping["unified_symbols"]:
                        unified_mapping["unified_symbols"][rapidtrader_symbol] = {
                            "rapidtrader_symbol": rapidtrader_symbol,
                            "brokers": {}
                        }

                    # Add broker-specific mapping
                    unified_mapping["unified_symbols"][rapidtrader_symbol]["brokers"][broker_name] = {
                        "broker_symbol": symbol_data.get("broker_symbol", rapidtrader_symbol),
                        "exchange": symbol_data.get("exchange", ""),
                        "segment": symbol_data.get("segment", ""),
                        "name": symbol_data.get("name", ""),
                        "additional_data": {k: v for k, v in symbol_data.items()
                                         if k not in ["symbol", "broker_symbol", "exchange", "segment", "name"]}
                    }

            # Update metadata
            unified_mapping["metadata"] = {
                "last_updated": datetime.now().isoformat(),
                "total_brokers": len(unified_mapping["brokers"]),
                "total_unified_symbols": len(unified_mapping["unified_symbols"]),
                "version": "1.0"
            }

            # Save unified mapping
            with open(self.unified_mapping_file, 'w') as f:
                json.dump(unified_mapping, f, indent=2)

            logger.info(f"Updated unified mapping with {len(symbols)} symbols from {broker_name}")

        except Exception as e:
            logger.error(f"Error updating unified mapping: {e}")

    def _download_fyers_symbols(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Download complete Fyers symbol master contract"""
        try:
            symbols = []

            # Method 1: Try Fyers Symbol Master API
            if config.get("client_id") and config.get("access_token"):
                try:
                    from broker.fyers_wrapper import FyersBroker
                    logger.info("Downloading Fyers master contract via API...")

                    fyers_broker = FyersBroker(
                        client_id=config.get("client_id"),
                        access_token=config.get("access_token"),
                        dry_run=True
                    )

                    if fyers_broker.fyers:
                        # Download complete symbol master for all exchanges
                        symbols = self._get_fyers_complete_master_contract(fyers_broker)
                        if symbols:
                            logger.info(f"Downloaded {len(symbols)} symbols via Fyers Symbol Master API")
                            return symbols

                except Exception as e:
                    logger.warning(f"Fyers Symbol Master API failed: {e}")

            # Method 2: Download from Fyers public symbol files
            try:
                logger.info("Downloading Fyers symbols from public master files...")
                symbols = self._download_fyers_master_files()
                if symbols:
                    logger.info(f"Downloaded {len(symbols)} symbols from Fyers master files")
                    return symbols

            except Exception as e:
                logger.warning(f"Fyers master files download failed: {e}")

            # Method 3: Use comprehensive fallback symbols
            logger.warning("All Fyers download methods failed, using comprehensive fallback")
            return self._get_comprehensive_fyers_symbols()

        except Exception as e:
            logger.error(f"Error downloading Fyers symbols: {e}")
            return self._get_comprehensive_fyers_symbols()

    def _get_fyers_complete_master_contract(self, fyers_broker) -> List[Dict[str, Any]]:
        """Download complete Fyers master contract using Symbol Master API"""
        symbols = []

        try:
            # Fyers exchanges to download
            exchanges = ["NSE", "BSE", "MCX", "NCDEX"]

            for exchange in exchanges:
                try:
                    logger.info(f"Downloading Fyers symbols for {exchange}...")

                    # Use Fyers Symbol Master API
                    response = fyers_broker.get_symbol_master(exchange=exchange)

                    if response.get('s') == 'ok' and 'd' in response:
                        exchange_symbols = response['d']

                        for symbol_data in exchange_symbols:
                            try:
                                # Parse Fyers symbol data
                                fyers_symbol = symbol_data.get('fyToken', '')
                                display_name = symbol_data.get('symbol_details', {}).get('name', '')
                                trading_symbol = symbol_data.get('symbol_details', {}).get('short_name', '')

                                if fyers_symbol and trading_symbol:
                                    # Extract base symbol from trading symbol
                                    base_symbol = trading_symbol.split('-')[0] if '-' in trading_symbol else trading_symbol

                                    symbol_info = {
                                        "symbol": base_symbol,
                                        "broker_symbol": fyers_symbol,
                                        "trading_symbol": trading_symbol,
                                        "name": display_name or f"{base_symbol} Limited",
                                        "exchange": exchange,
                                        "segment": self._extract_fyers_segment(trading_symbol),
                                        "token": symbol_data.get('token', ''),
                                        "lot_size": symbol_data.get('lot_size', 1),
                                        "tick_size": symbol_data.get('tick_size', 0.01),
                                        "instrument_type": symbol_data.get('instrument_type', ''),
                                        "expiry": symbol_data.get('expiry', ''),
                                        "strike": symbol_data.get('strike_price', 0),
                                        "source": "FYERS_MASTER_API"
                                    }
                                    symbols.append(symbol_info)

                            except Exception as e:
                                logger.debug(f"Error parsing Fyers symbol data: {e}")
                                continue

                        logger.info(f"Downloaded {len(exchange_symbols)} symbols from Fyers {exchange}")

                except Exception as e:
                    logger.warning(f"Error downloading Fyers {exchange} symbols: {e}")
                    continue

                # Rate limiting
                time.sleep(0.5)

            return symbols

        except Exception as e:
            logger.error(f"Error in Fyers complete master contract download: {e}")
            return []

    def _download_fyers_master_files(self) -> List[Dict[str, Any]]:
        """Download Fyers symbols from public master files"""
        symbols = []

        try:
            # Multiple Fyers symbol file URLs
            urls = [
                "https://public.fyers.in/sym_details/NSE_CM.csv",
                "https://public.fyers.in/sym_details/NSE_FO.csv",
                "https://public.fyers.in/sym_details/BSE_CM.csv",
                "https://public.fyers.in/sym_details/BSE_FO.csv",
                "https://public.fyers.in/sym_details/MCX_COM.csv",
                "https://public.fyers.in/sym_details/NCDEX_COM.csv",
                "https://api-t1.fyers.in/data-rest/v2/symbols",  # Alternative API
                "https://public.fyers.in/symbols.txt"  # Fallback
            ]

            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Accept": "text/csv,application/json,text/plain,*/*",
                "Connection": "keep-alive"
            }

            for url in urls:
                try:
                    logger.info(f"Trying Fyers URL: {url}")
                    response = requests.get(url, headers=headers, timeout=self.timeout)
                    response.raise_for_status()

                    content = response.text.strip()
                    if not content:
                        continue

                    # Parse based on file type
                    if url.endswith('.csv'):
                        parsed_symbols = self._parse_fyers_csv_master(content, url)
                    elif 'symbols.txt' in url:
                        parsed_symbols = self._parse_fyers_txt_master(content)
                    else:
                        # Try JSON format
                        try:
                            data = response.json()
                            parsed_symbols = self._parse_fyers_json_master(data)
                        except:
                            parsed_symbols = self._parse_fyers_txt_master(content)

                    if parsed_symbols:
                        symbols.extend(parsed_symbols)
                        logger.info(f"Parsed {len(parsed_symbols)} symbols from {url}")

                except Exception as e:
                    logger.debug(f"Failed to download from {url}: {e}")
                    continue

            # Remove duplicates based on broker_symbol
            unique_symbols = {}
            for symbol in symbols:
                broker_symbol = symbol.get('broker_symbol', '')
                if broker_symbol and broker_symbol not in unique_symbols:
                    unique_symbols[broker_symbol] = symbol

            return list(unique_symbols.values())

        except Exception as e:
            logger.error(f"Error downloading Fyers master files: {e}")
            return []

    def _parse_fyers_csv_master(self, content: str, url: str) -> List[Dict[str, Any]]:
        """Parse Fyers CSV master files"""
        symbols = []

        try:
            from io import StringIO
            df = pd.read_csv(StringIO(content))

            # Extract exchange from URL
            exchange = "NSE"
            if "BSE" in url:
                exchange = "BSE"
            elif "MCX" in url:
                exchange = "MCX"
            elif "NCDEX" in url:
                exchange = "NCDEX"

            for _, row in df.iterrows():
                try:
                    # Adapt to Fyers CSV structure
                    fyers_symbol = str(row.get('Fytoken', row.get('Symbol', ''))).strip()
                    name = str(row.get('CompanyName', row.get('Name', ''))).strip()
                    trading_symbol = str(row.get('TradingSymbol', row.get('Symbol', ''))).strip()

                    if fyers_symbol and trading_symbol:
                        base_symbol = trading_symbol.split('-')[0] if '-' in trading_symbol else trading_symbol

                        symbol_data = {
                            "symbol": base_symbol,
                            "broker_symbol": fyers_symbol,
                            "trading_symbol": trading_symbol,
                            "name": name or f"{base_symbol} Limited",
                            "exchange": exchange,
                            "segment": self._extract_fyers_segment(trading_symbol),
                            "lot_size": int(row.get('LotSize', 1)),
                            "tick_size": float(row.get('TickSize', 0.01)),
                            "source": "FYERS_CSV_MASTER"
                        }
                        symbols.append(symbol_data)

                except Exception as e:
                    logger.debug(f"Error parsing CSV row: {e}")
                    continue

        except Exception as e:
            logger.debug(f"Error parsing Fyers CSV: {e}")

        return symbols

    def _parse_fyers_txt_master(self, content: str) -> List[Dict[str, Any]]:
        """Parse Fyers TXT master file"""
        symbols = []

        try:
            lines = content.strip().split('\n')
            for line in lines:
                line = line.strip()
                if line and ':' in line:
                    symbol_data = self._parse_fyers_symbol_line(line)
                    if symbol_data:
                        symbol_data["source"] = "FYERS_TXT_MASTER"
                        symbols.append(symbol_data)

        except Exception as e:
            logger.debug(f"Error parsing Fyers TXT: {e}")

        return symbols

    def _parse_fyers_json_master(self, data: Any) -> List[Dict[str, Any]]:
        """Parse Fyers JSON master data"""
        symbols = []

        try:
            if isinstance(data, dict):
                symbol_list = data.get('symbols', data.get('data', data))
            else:
                symbol_list = data

            if isinstance(symbol_list, list):
                for item in symbol_list:
                    if isinstance(item, dict):
                        symbol_data = self._create_fyers_symbol_from_json(item)
                        if symbol_data:
                            symbols.append(symbol_data)

        except Exception as e:
            logger.debug(f"Error parsing Fyers JSON: {e}")

        return symbols

    def _extract_fyers_segment(self, trading_symbol: str) -> str:
        """Extract segment from Fyers trading symbol"""
        if '-EQ' in trading_symbol:
            return 'EQ'
        elif '-FUT' in trading_symbol:
            return 'FUT'
        elif '-CE' in trading_symbol or '-PE' in trading_symbol:
            return 'OPT'
        else:
            return 'EQ'  # Default

    def _create_fyers_symbol_from_json(self, item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create symbol data from Fyers JSON item"""
        try:
            fyers_symbol = item.get('fyToken', item.get('symbol', ''))
            trading_symbol = item.get('trading_symbol', item.get('name', ''))

            if fyers_symbol and trading_symbol:
                base_symbol = trading_symbol.split('-')[0] if '-' in trading_symbol else trading_symbol

                return {
                    "symbol": base_symbol,
                    "broker_symbol": fyers_symbol,
                    "trading_symbol": trading_symbol,
                    "name": item.get('description', f"{base_symbol} Limited"),
                    "exchange": item.get('exchange', 'NSE'),
                    "segment": self._extract_fyers_segment(trading_symbol),
                    "source": "FYERS_JSON_MASTER"
                }

        except Exception as e:
            logger.debug(f"Error creating symbol from JSON: {e}")

        return None

    def _get_comprehensive_fyers_symbols(self) -> List[Dict[str, Any]]:
        """Get comprehensive Fyers symbols as fallback"""
        # Much larger fallback list with popular symbols
        comprehensive_symbols = [
            # Top 50 NSE stocks
            "NSE:RELIANCE-EQ", "NSE:TCS-EQ", "NSE:HDFCBANK-EQ", "NSE:ICICIBANK-EQ",
            "NSE:INFY-EQ", "NSE:HINDUNILVR-EQ", "NSE:ITC-EQ", "NSE:SBIN-EQ",
            "NSE:BHARTIARTL-EQ", "NSE:KOTAKBANK-EQ", "NSE:LT-EQ", "NSE:ASIANPAINT-EQ",
            "NSE:AXISBANK-EQ", "NSE:MARUTI-EQ", "NSE:NESTLEIND-EQ", "NSE:HCLTECH-EQ",
            "NSE:WIPRO-EQ", "NSE:ULTRACEMCO-EQ", "NSE:BAJFINANCE-EQ", "NSE:TITAN-EQ",
            "NSE:SUNPHARMA-EQ", "NSE:ONGC-EQ", "NSE:NTPC-EQ", "NSE:POWERGRID-EQ",
            "NSE:COALINDIA-EQ", "NSE:TATAMOTORS-EQ", "NSE:BAJAJFINSV-EQ", "NSE:HDFCLIFE-EQ",
            "NSE:TECHM-EQ", "NSE:GRASIM-EQ", "NSE:ADANIPORTS-EQ", "NSE:JSWSTEEL-EQ",
            "NSE:INDUSINDBK-EQ", "NSE:TATASTEEL-EQ", "NSE:CIPLA-EQ", "NSE:DRREDDY-EQ",
            "NSE:EICHERMOT-EQ", "NSE:BRITANNIA-EQ", "NSE:DIVISLAB-EQ", "NSE:APOLLOHOSP-EQ",
            "NSE:BPCL-EQ", "NSE:HEROMOTOCO-EQ", "NSE:SHREECEM-EQ", "NSE:PIDILITIND-EQ",
            "NSE:BAJAJ-AUTO-EQ", "NSE:SBILIFE-EQ", "NSE:ADANIENT-EQ", "NSE:TATACONSUM-EQ",

            # Popular futures
            "NSE:NIFTY24JAN25FUT", "NSE:BANKNIFTY24JAN25FUT", "NSE:RELIANCE24JAN25FUT",
            "NSE:TCS24JAN25FUT", "NSE:HDFCBANK24JAN25FUT", "NSE:ICICIBANK24JAN25FUT",

            # Popular options
            "NSE:NIFTY24JAN2524000CE", "NSE:NIFTY24JAN2524000PE", "NSE:BANKNIFTY24JAN2550000CE",
            "NSE:BANKNIFTY24JAN2550000PE", "NSE:RELIANCE24JAN253000CE", "NSE:RELIANCE24JAN253000PE",

            # BSE stocks
            "BSE:RELIANCE-EQ", "BSE:TCS-EQ", "BSE:HDFCBANK-EQ", "BSE:ICICIBANK-EQ",
            "BSE:INFY-EQ", "BSE:HINDUNILVR-EQ", "BSE:ITC-EQ", "BSE:SBIN-EQ",

            # MCX commodities
            "MCX:CRUDEOIL24JAN25FUT", "MCX:GOLD24FEB25FUT", "MCX:SILVER24MAR25FUT",
            "MCX:NATURALGAS24JAN25FUT", "MCX:COPPER24JAN25FUT", "MCX:ZINC24JAN25FUT"
        ]

        symbols = []
        for fyers_symbol in comprehensive_symbols:
            symbol_data = self._parse_fyers_symbol_line(fyers_symbol)
            if symbol_data:
                symbol_data["source"] = "COMPREHENSIVE_FALLBACK"
                symbols.append(symbol_data)

        logger.info(f"Using comprehensive fallback with {len(symbols)} Fyers symbols")
        return symbols

    def _download_dhan_symbols(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Download complete DhanHQ symbol master contract"""
        try:
            symbols = []

            # Method 1: Try DhanHQ get_instruments API for all segments
            if config.get("client_id") and config.get("access_token"):
                try:
                    from broker.dhan_wrapper import DhanBroker
                    logger.info("Downloading DhanHQ master contract via API...")

                    dhan_broker = DhanBroker(
                        client_id=config.get("client_id"),
                        access_token=config.get("access_token"),
                        dry_run=True
                    )

                    symbols = self._get_dhan_complete_master_contract(dhan_broker)
                    if symbols:
                        logger.info(f"Downloaded {len(symbols)} symbols via DhanHQ get_instruments API")
                        return symbols

                except Exception as e:
                    logger.warning(f"DhanHQ get_instruments API failed: {e}")

            # Method 2: Try downloading from DhanHQ public files
            try:
                logger.info("Downloading DhanHQ symbols from public master files...")
                symbols = self._download_dhan_master_files()
                if symbols:
                    logger.info(f"Downloaded {len(symbols)} symbols from DhanHQ master files")
                    return symbols

            except Exception as e:
                logger.warning(f"DhanHQ master files download failed: {e}")

            # Method 3: Use comprehensive fallback symbols
            logger.warning("All DhanHQ download methods failed, using comprehensive fallback")
            return self._get_comprehensive_dhan_symbols()

        except Exception as e:
            logger.error(f"Error downloading DhanHQ symbols: {e}")
            return self._get_comprehensive_dhan_symbols()

    def _get_dhan_complete_master_contract(self, dhan_broker) -> List[Dict[str, Any]]:
        """Download complete DhanHQ master contract using get_instruments API"""
        symbols = []

        try:
            # DhanHQ exchange segments to download
            exchange_segments = [
                "NSE_EQ",    # NSE Equity
                "BSE_EQ",    # BSE Equity
                "NSE_FNO",   # NSE Futures & Options
                "BSE_FNO",   # BSE Futures & Options
                "MCX_COM",   # MCX Commodities
                "CUR_BCD",   # Currency BSE
                "CUR_NSE"    # Currency NSE
            ]

            for exchange_segment in exchange_segments:
                try:
                    logger.info(f"Downloading DhanHQ instruments for {exchange_segment}...")

                    # Use DhanHQ get_instruments API
                    instruments = dhan_broker.get_instruments(exchange_segment=exchange_segment)

                    if instruments and isinstance(instruments, list):
                        for instrument in instruments:
                            try:
                                # Parse DhanHQ instrument data
                                security_id = str(instrument.get("SEM_SMST_SECURITY_ID", "")).strip()
                                trading_symbol = str(instrument.get("SEM_TRADING_SYMBOL", "")).strip()
                                custom_symbol = str(instrument.get("SEM_CUSTOM_SYMBOL", "")).strip()
                                company_name = str(instrument.get("SEM_COMPANY_NAME", "")).strip()

                                if security_id and trading_symbol:
                                    # Extract base symbol
                                    base_symbol = trading_symbol.split('-')[0] if '-' in trading_symbol else trading_symbol

                                    # Extract exchange and segment
                                    exchange = exchange_segment.split('_')[0]  # NSE, BSE, MCX, CUR
                                    segment = exchange_segment.split('_')[1] if '_' in exchange_segment else "EQ"

                                    symbol_info = {
                                        "symbol": base_symbol,
                                        "broker_symbol": security_id,
                                        "trading_symbol": trading_symbol,
                                        "custom_symbol": custom_symbol,
                                        "name": company_name or custom_symbol or f"{base_symbol} Limited",
                                        "exchange": exchange,
                                        "segment": segment,
                                        "exchange_segment": exchange_segment,
                                        "security_id": security_id,
                                        "lot_size": int(instrument.get("SEM_LOT_UNITS", 1)),
                                        "tick_size": float(instrument.get("SEM_TICK_SIZE", 0.01)),
                                        "isin": str(instrument.get("SEM_ISIN", "")).strip(),
                                        "expiry_date": str(instrument.get("SEM_EXPIRY_DATE", "")).strip(),
                                        "strike_price": float(instrument.get("SEM_STRIKE_PRICE", 0)),
                                        "option_type": str(instrument.get("SEM_OPTION_TYPE", "")).strip(),
                                        "source": "DHAN_INSTRUMENTS_API"
                                    }
                                    symbols.append(symbol_info)

                            except Exception as e:
                                logger.debug(f"Error parsing DhanHQ instrument data: {e}")
                                continue

                        logger.info(f"Downloaded {len(instruments)} instruments from DhanHQ {exchange_segment}")

                except Exception as e:
                    logger.warning(f"Error downloading DhanHQ {exchange_segment} instruments: {e}")
                    continue

                # Rate limiting
                time.sleep(0.5)

            return symbols

        except Exception as e:
            logger.error(f"Error in DhanHQ complete master contract download: {e}")
            return []

    def _download_dhan_master_files(self) -> List[Dict[str, Any]]:
        """Download DhanHQ symbols from public master files"""
        symbols = []

        try:
            # DhanHQ doesn't provide public master files like Fyers
            # But we can try some common URLs or use API endpoints
            urls = [
                "https://images.dhan.co/api-data/api-scrip-master.csv",
                "https://api.dhan.co/v2/instruments",
                "https://dhanhq.co/api/instruments/download"
            ]

            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Accept": "text/csv,application/json,*/*",
                "Connection": "keep-alive"
            }

            for url in urls:
                try:
                    logger.info(f"Trying DhanHQ URL: {url}")
                    response = requests.get(url, headers=headers, timeout=self.timeout)
                    response.raise_for_status()

                    content = response.text.strip()
                    if not content:
                        continue

                    # Parse based on content type
                    if url.endswith('.csv') or 'csv' in response.headers.get('content-type', ''):
                        parsed_symbols = self._parse_dhan_csv_master(content)
                    else:
                        # Try JSON format
                        try:
                            data = response.json()
                            parsed_symbols = self._parse_dhan_json_master(data)
                        except:
                            # Try as CSV anyway
                            parsed_symbols = self._parse_dhan_csv_master(content)

                    if parsed_symbols:
                        symbols.extend(parsed_symbols)
                        logger.info(f"Parsed {len(parsed_symbols)} symbols from {url}")

                except Exception as e:
                    logger.debug(f"Failed to download from {url}: {e}")
                    continue

            # Remove duplicates based on security_id
            unique_symbols = {}
            for symbol in symbols:
                security_id = symbol.get('broker_symbol', '')
                if security_id and security_id not in unique_symbols:
                    unique_symbols[security_id] = symbol

            return list(unique_symbols.values())

        except Exception as e:
            logger.error(f"Error downloading DhanHQ master files: {e}")
            return []

    def _parse_dhan_csv_master(self, content: str) -> List[Dict[str, Any]]:
        """Parse DhanHQ CSV master files"""
        symbols = []

        try:
            from io import StringIO
            df = pd.read_csv(StringIO(content))

            for _, row in df.iterrows():
                try:
                    # Adapt to DhanHQ CSV structure
                    security_id = str(row.get('SEM_SMST_SECURITY_ID', row.get('SecurityId', ''))).strip()
                    trading_symbol = str(row.get('SEM_TRADING_SYMBOL', row.get('TradingSymbol', ''))).strip()
                    company_name = str(row.get('SEM_COMPANY_NAME', row.get('CompanyName', ''))).strip()
                    exchange_segment = str(row.get('SEM_EXM_EXCH_ID', row.get('Exchange', ''))).strip()

                    if security_id and trading_symbol:
                        base_symbol = trading_symbol.split('-')[0] if '-' in trading_symbol else trading_symbol

                        # Map exchange segment
                        if exchange_segment in ['NSE', 'NSE_EQ']:
                            exchange = 'NSE'
                            segment = 'EQ'
                        elif exchange_segment in ['BSE', 'BSE_EQ']:
                            exchange = 'BSE'
                            segment = 'EQ'
                        elif exchange_segment in ['NSE_FNO']:
                            exchange = 'NSE'
                            segment = 'FNO'
                        elif exchange_segment in ['MCX_COM']:
                            exchange = 'MCX'
                            segment = 'COM'
                        else:
                            exchange = 'NSE'
                            segment = 'EQ'

                        symbol_data = {
                            "symbol": base_symbol,
                            "broker_symbol": security_id,
                            "trading_symbol": trading_symbol,
                            "name": company_name or f"{base_symbol} Limited",
                            "exchange": exchange,
                            "segment": segment,
                            "exchange_segment": exchange_segment,
                            "security_id": security_id,
                            "lot_size": int(row.get('SEM_LOT_UNITS', row.get('LotSize', 1))),
                            "tick_size": float(row.get('SEM_TICK_SIZE', row.get('TickSize', 0.01))),
                            "source": "DHAN_CSV_MASTER"
                        }
                        symbols.append(symbol_data)

                except Exception as e:
                    logger.debug(f"Error parsing DhanHQ CSV row: {e}")
                    continue

        except Exception as e:
            logger.debug(f"Error parsing DhanHQ CSV: {e}")

        return symbols

    def _parse_dhan_json_master(self, data: Any) -> List[Dict[str, Any]]:
        """Parse DhanHQ JSON master data"""
        symbols = []

        try:
            if isinstance(data, dict):
                instrument_list = data.get('instruments', data.get('data', data))
            else:
                instrument_list = data

            if isinstance(instrument_list, list):
                for item in instrument_list:
                    if isinstance(item, dict):
                        symbol_data = self._create_dhan_symbol_from_json(item)
                        if symbol_data:
                            symbols.append(symbol_data)

        except Exception as e:
            logger.debug(f"Error parsing DhanHQ JSON: {e}")

        return symbols

    def _create_dhan_symbol_from_json(self, item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create symbol data from DhanHQ JSON item"""
        try:
            security_id = str(item.get('security_id', item.get('SEM_SMST_SECURITY_ID', ''))).strip()
            trading_symbol = str(item.get('trading_symbol', item.get('SEM_TRADING_SYMBOL', ''))).strip()

            if security_id and trading_symbol:
                base_symbol = trading_symbol.split('-')[0] if '-' in trading_symbol else trading_symbol

                return {
                    "symbol": base_symbol,
                    "broker_symbol": security_id,
                    "trading_symbol": trading_symbol,
                    "name": item.get('company_name', f"{base_symbol} Limited"),
                    "exchange": item.get('exchange', 'NSE'),
                    "segment": item.get('segment', 'EQ'),
                    "security_id": security_id,
                    "source": "DHAN_JSON_MASTER"
                }

        except Exception as e:
            logger.debug(f"Error creating DhanHQ symbol from JSON: {e}")

        return None

    def _get_comprehensive_dhan_symbols(self) -> List[Dict[str, Any]]:
        """Get comprehensive DhanHQ symbols as fallback"""
        # Comprehensive DhanHQ symbols with security IDs
        comprehensive_symbols = [
            # Top NSE stocks with security IDs
            {"symbol": "RELIANCE", "security_id": "2885", "name": "Reliance Industries Ltd", "exchange": "NSE"},
            {"symbol": "TCS", "security_id": "11536", "name": "Tata Consultancy Services Ltd", "exchange": "NSE"},
            {"symbol": "HDFCBANK", "security_id": "1333", "name": "HDFC Bank Ltd", "exchange": "NSE"},
            {"symbol": "ICICIBANK", "security_id": "4963", "name": "ICICI Bank Ltd", "exchange": "NSE"},
            {"symbol": "INFY", "security_id": "1594", "name": "Infosys Ltd", "exchange": "NSE"},
            {"symbol": "HINDUNILVR", "security_id": "1394", "name": "Hindustan Unilever Ltd", "exchange": "NSE"},
            {"symbol": "ITC", "security_id": "1660", "name": "ITC Ltd", "exchange": "NSE"},
            {"symbol": "SBIN", "security_id": "3045", "name": "State Bank of India", "exchange": "NSE"},
            {"symbol": "BHARTIARTL", "security_id": "10604", "name": "Bharti Airtel Ltd", "exchange": "NSE"},
            {"symbol": "KOTAKBANK", "security_id": "1922", "name": "Kotak Mahindra Bank Ltd", "exchange": "NSE"},
            {"symbol": "LT", "security_id": "2031", "name": "Larsen & Toubro Ltd", "exchange": "NSE"},
            {"symbol": "ASIANPAINT", "security_id": "249", "name": "Asian Paints Ltd", "exchange": "NSE"},
            {"symbol": "AXISBANK", "security_id": "5900", "name": "Axis Bank Ltd", "exchange": "NSE"},
            {"symbol": "MARUTI", "security_id": "2031", "name": "Maruti Suzuki India Ltd", "exchange": "NSE"},
            {"symbol": "NESTLEIND", "security_id": "17963", "name": "Nestle India Ltd", "exchange": "NSE"},
            {"symbol": "HCLTECH", "security_id": "7229", "name": "HCL Technologies Ltd", "exchange": "NSE"},
            {"symbol": "WIPRO", "security_id": "3787", "name": "Wipro Ltd", "exchange": "NSE"},
            {"symbol": "ULTRACEMCO", "security_id": "11532", "name": "UltraTech Cement Ltd", "exchange": "NSE"},
            {"symbol": "BAJFINANCE", "security_id": "317", "name": "Bajaj Finance Ltd", "exchange": "NSE"},
            {"symbol": "TITAN", "security_id": "3506", "name": "Titan Company Ltd", "exchange": "NSE"},

            # Popular BSE stocks
            {"symbol": "RELIANCE", "security_id": "500325", "name": "Reliance Industries Ltd", "exchange": "BSE"},
            {"symbol": "TCS", "security_id": "532540", "name": "Tata Consultancy Services Ltd", "exchange": "BSE"},
            {"symbol": "HDFCBANK", "security_id": "500180", "name": "HDFC Bank Ltd", "exchange": "BSE"},
            {"symbol": "ICICIBANK", "security_id": "532174", "name": "ICICI Bank Ltd", "exchange": "BSE"},
            {"symbol": "INFY", "security_id": "500209", "name": "Infosys Ltd", "exchange": "BSE"},

            # Popular F&O instruments
            {"symbol": "NIFTY", "security_id": "26000", "name": "Nifty 50", "exchange": "NSE", "segment": "FNO"},
            {"symbol": "BANKNIFTY", "security_id": "26009", "name": "Bank Nifty", "exchange": "NSE", "segment": "FNO"},
            {"symbol": "FINNIFTY", "security_id": "26037", "name": "Fin Nifty", "exchange": "NSE", "segment": "FNO"},

            # MCX commodities
            {"symbol": "CRUDEOIL", "security_id": "236", "name": "Crude Oil", "exchange": "MCX", "segment": "COM"},
            {"symbol": "GOLD", "security_id": "220", "name": "Gold", "exchange": "MCX", "segment": "COM"},
            {"symbol": "SILVER", "security_id": "229", "name": "Silver", "exchange": "MCX", "segment": "COM"},
            {"symbol": "NATURALGAS", "security_id": "232", "name": "Natural Gas", "exchange": "MCX", "segment": "COM"}
        ]

        symbols = []
        for item in comprehensive_symbols:
            symbol_data = {
                "symbol": item["symbol"],
                "broker_symbol": item["security_id"],
                "trading_symbol": item["symbol"],
                "name": item["name"],
                "exchange": item["exchange"],
                "segment": item.get("segment", "EQ"),
                "security_id": item["security_id"],
                "lot_size": 1,
                "tick_size": 0.01,
                "source": "COMPREHENSIVE_FALLBACK"
            }
            symbols.append(symbol_data)

        logger.info(f"Using comprehensive fallback with {len(symbols)} DhanHQ symbols")
        return symbols

    def _download_zerodha_symbols(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Download symbols from Zerodha broker"""
        try:
            # Zerodha provides instrument files
            logger.info("Downloading Zerodha symbols from instrument files...")

            # Try to download from Zerodha's public instrument files
            urls = [
                "https://api.kite.trade/instruments",
                "https://api.kite.trade/instruments/NSE",
                "https://api.kite.trade/instruments/BSE"
            ]

            symbols = []
            for url in urls:
                try:
                    response = requests.get(url, timeout=self.timeout)
                    if response.status_code == 200:
                        # Parse CSV format
                        from io import StringIO
                        df = pd.read_csv(StringIO(response.text))

                        for _, row in df.iterrows():
                            symbol_data = {
                                "symbol": str(row.get("tradingsymbol", "")).strip(),
                                "broker_symbol": str(row.get("tradingsymbol", "")).strip(),
                                "name": str(row.get("name", "")).strip(),
                                "exchange": str(row.get("exchange", "")).strip(),
                                "segment": str(row.get("segment", "")).strip(),
                                "instrument_token": str(row.get("instrument_token", "")).strip(),
                                "source": "ZERODHA_API"
                            }

                            if symbol_data["symbol"]:
                                symbols.append(symbol_data)

                        if symbols:
                            break

                except Exception as e:
                    logger.debug(f"Failed to download from {url}: {e}")
                    continue

            if symbols:
                logger.info(f"Downloaded {len(symbols)} Zerodha symbols")
                return symbols
            else:
                return self._get_fallback_zerodha_symbols()

        except Exception as e:
            logger.error(f"Error downloading Zerodha symbols: {e}")
            return self._get_fallback_zerodha_symbols()

    def _download_upstox_symbols(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Download symbols from Upstox broker"""
        try:
            logger.info("Upstox symbol download not implemented yet, using fallback")
            return self._get_fallback_upstox_symbols()
        except Exception as e:
            logger.error(f"Error downloading Upstox symbols: {e}")
            return self._get_fallback_upstox_symbols()

    def _download_angel_symbols(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Download symbols from Angel Broking"""
        try:
            logger.info("Angel Broking symbol download not implemented yet, using fallback")
            return self._get_fallback_angel_symbols()
        except Exception as e:
            logger.error(f"Error downloading Angel symbols: {e}")
            return self._get_fallback_angel_symbols()

    def _download_iifl_symbols(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Download symbols from IIFL broker"""
        try:
            logger.info("IIFL symbol download not implemented yet, using fallback")
            return self._get_fallback_iifl_symbols()
        except Exception as e:
            logger.error(f"Error downloading IIFL symbols: {e}")
            return self._get_fallback_iifl_symbols()

    def _get_fyers_symbols_via_api(self, fyers_broker) -> List[Dict[str, Any]]:
        """Get Fyers symbols using API"""
        symbols = []
        try:
            # Get market status to verify API is working
            market_status = fyers_broker._make_api_call("market_status", {})
            if market_status.get('s') != 'ok':
                logger.warning("Fyers API not responding properly")
                return []

            # Use popular symbols to get their details
            popular_symbols = [
                "NSE:SBIN-EQ", "NSE:RELIANCE-EQ", "NSE:TCS-EQ", "NSE:HDFCBANK-EQ",
                "NSE:ICICIBANK-EQ", "NSE:INFY-EQ", "NSE:HINDUNILVR-EQ", "NSE:ITC-EQ",
                "NSE:BHARTIARTL-EQ", "NSE:KOTAKBANK-EQ", "NSE:LT-EQ", "NSE:ASIANPAINT-EQ",
                "NSE:AXISBANK-EQ", "NSE:MARUTI-EQ", "NSE:NESTLEIND-EQ", "NSE:HCLTECH-EQ",
                "NSE:WIPRO-EQ", "NSE:ULTRACEMCO-EQ", "NSE:BAJFINANCE-EQ", "NSE:TITAN-EQ"
            ]

            # Get quotes for these symbols
            for symbol_batch in [popular_symbols[i:i+20] for i in range(0, len(popular_symbols), 20)]:
                try:
                    quotes_data = {"symbols": ",".join(symbol_batch)}
                    quotes_response = fyers_broker._make_api_call("quotes", quotes_data)

                    if quotes_response.get('s') == 'ok' and 'd' in quotes_response:
                        for symbol_data in quotes_response['d']:
                            if symbol_data and 'n' in symbol_data:
                                fyers_symbol = symbol_data.get('n', '')
                                if ':' in fyers_symbol and '-' in fyers_symbol:
                                    parts = fyers_symbol.split(':')
                                    if len(parts) == 2:
                                        exchange = parts[0]
                                        symbol_part = parts[1].split('-')[0]

                                        symbol_info = {
                                            "symbol": symbol_part,
                                            "broker_symbol": fyers_symbol,
                                            "name": f"{symbol_part} Limited",
                                            "exchange": "NSE" if exchange == "NSE" else exchange,
                                            "segment": "EQ",
                                            "source": "FYERS_API",
                                            "ltp": symbol_data.get('lp', 0),
                                            "volume": symbol_data.get('v', 0)
                                        }
                                        symbols.append(symbol_info)

                except Exception as e:
                    logger.debug(f"Error processing symbol batch: {e}")
                    continue

                time.sleep(0.1)  # Rate limiting

            return symbols

        except Exception as e:
            logger.error(f"Error in Fyers API download: {e}")
            return []

    def _download_fyers_from_url(self) -> List[Dict[str, Any]]:
        """Download symbols from Fyers public URL"""
        symbols = []
        try:
            urls = [
                "https://public.fyers.in/symbols.txt",
                "https://public.fyers.in/symbols.csv"
            ]

            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Accept": "text/plain,text/csv,*/*",
                "Connection": "keep-alive"
            }

            for url in urls:
                try:
                    response = requests.get(url, headers=headers, timeout=self.timeout)
                    response.raise_for_status()

                    content = response.text.strip()
                    if not content:
                        continue

                    # Parse content
                    lines = content.strip().split('\n')
                    for line in lines:
                        line = line.strip()
                        if line and ':' in line and '-' in line:
                            symbol_data = self._parse_fyers_symbol_line(line)
                            if symbol_data:
                                symbols.append(symbol_data)

                    if symbols:
                        logger.info(f"Downloaded {len(symbols)} symbols from {url}")
                        return symbols

                except Exception as e:
                    logger.warning(f"Failed to download from {url}: {e}")
                    continue

            return []

        except Exception as e:
            logger.error(f"Error downloading from Fyers URL: {e}")
            return []

    def _parse_fyers_symbol_line(self, line: str) -> Optional[Dict[str, Any]]:
        """Parse a single Fyers symbol line"""
        try:
            if ':' in line and '-' in line:
                parts = line.split(':')
                if len(parts) == 2:
                    exchange = parts[0].strip()
                    symbol_part = parts[1].split('-')[0].strip()
                    segment = parts[1].split('-')[1].strip() if '-' in parts[1] else 'EQ'

                    return {
                        "symbol": symbol_part,
                        "broker_symbol": line,
                        "name": f"{symbol_part} Limited",
                        "exchange": "NSE" if exchange == "NSE" else exchange,
                        "segment": segment,
                        "source": "FYERS_URL"
                    }
        except Exception as e:
            logger.debug(f"Error parsing symbol line {line}: {e}")

        return None

    def _get_dhan_symbols_via_api(self, dhan_broker) -> List[Dict[str, Any]]:
        """Get DhanHQ symbols using API"""
        symbols = []
        try:
            # DhanHQ provides get_instruments method
            exchanges = ["NSE_EQ", "BSE_EQ", "NSE_FNO", "MCX_COM"]

            for exchange in exchanges:
                try:
                    instruments = dhan_broker.get_instruments(exchange_segment=exchange)
                    if instruments:
                        for instrument in instruments:
                            symbol_data = {
                                "symbol": str(instrument.get("SEM_TRADING_SYMBOL", "")).strip(),
                                "broker_symbol": str(instrument.get("SEM_SMST_SECURITY_ID", "")).strip(),
                                "name": str(instrument.get("SEM_CUSTOM_SYMBOL", "")).strip(),
                                "exchange": exchange.split('_')[0],  # NSE, BSE, etc.
                                "segment": exchange.split('_')[1] if '_' in exchange else "EQ",
                                "security_id": str(instrument.get("SEM_SMST_SECURITY_ID", "")).strip(),
                                "source": "DHAN_API"
                            }

                            if symbol_data["symbol"]:
                                symbols.append(symbol_data)

                except Exception as e:
                    logger.debug(f"Error getting instruments for {exchange}: {e}")
                    continue

            return symbols

        except Exception as e:
            logger.error(f"Error in DhanHQ API download: {e}")
            return []

    def _get_fallback_fyers_symbols(self) -> List[Dict[str, Any]]:
        """Get fallback Fyers symbols"""
        fallback_symbols = [
            "NSE:RELIANCE-EQ", "NSE:TCS-EQ", "NSE:HDFCBANK-EQ", "NSE:ICICIBANK-EQ",
            "NSE:INFY-EQ", "NSE:HINDUNILVR-EQ", "NSE:ITC-EQ", "NSE:SBIN-EQ",
            "NSE:BHARTIARTL-EQ", "NSE:KOTAKBANK-EQ", "NSE:LT-EQ", "NSE:ASIANPAINT-EQ",
            "NSE:AXISBANK-EQ", "NSE:MARUTI-EQ", "NSE:NESTLEIND-EQ", "NSE:HCLTECH-EQ",
            "NSE:WIPRO-EQ", "NSE:ULTRACEMCO-EQ", "NSE:BAJFINANCE-EQ", "NSE:TITAN-EQ"
        ]

        symbols = []
        for fyers_symbol in fallback_symbols:
            symbol_data = self._parse_fyers_symbol_line(fyers_symbol)
            if symbol_data:
                symbol_data["source"] = "FALLBACK"
                symbols.append(symbol_data)

        logger.info(f"Using fallback list with {len(symbols)} Fyers symbols")
        return symbols

    def _get_fallback_dhan_symbols(self) -> List[Dict[str, Any]]:
        """Get fallback DhanHQ symbols"""
        # Popular DhanHQ symbols with security IDs
        fallback_symbols = [
            {"symbol": "RELIANCE", "security_id": "2885", "name": "Reliance Industries Ltd"},
            {"symbol": "TCS", "security_id": "11536", "name": "Tata Consultancy Services Ltd"},
            {"symbol": "HDFCBANK", "security_id": "1333", "name": "HDFC Bank Ltd"},
            {"symbol": "ICICIBANK", "security_id": "4963", "name": "ICICI Bank Ltd"},
            {"symbol": "INFY", "security_id": "1594", "name": "Infosys Ltd"},
            {"symbol": "HINDUNILVR", "security_id": "1394", "name": "Hindustan Unilever Ltd"},
            {"symbol": "ITC", "security_id": "1660", "name": "ITC Ltd"},
            {"symbol": "SBIN", "security_id": "3045", "name": "State Bank of India"},
            {"symbol": "BHARTIARTL", "security_id": "10604", "name": "Bharti Airtel Ltd"},
            {"symbol": "KOTAKBANK", "security_id": "1922", "name": "Kotak Mahindra Bank Ltd"}
        ]

        symbols = []
        for item in fallback_symbols:
            symbol_data = {
                "symbol": item["symbol"],
                "broker_symbol": item["security_id"],
                "name": item["name"],
                "exchange": "NSE",
                "segment": "EQ",
                "security_id": item["security_id"],
                "source": "FALLBACK"
            }
            symbols.append(symbol_data)

        logger.info(f"Using fallback list with {len(symbols)} DhanHQ symbols")
        return symbols

    def _get_fallback_zerodha_symbols(self) -> List[Dict[str, Any]]:
        """Get fallback Zerodha symbols"""
        fallback_symbols = [
            "RELIANCE", "TCS", "HDFCBANK", "ICICIBANK", "INFY", "HINDUNILVR", "ITC",
            "SBIN", "BHARTIARTL", "KOTAKBANK", "LT", "ASIANPAINT", "AXISBANK", "MARUTI"
        ]

        symbols = []
        for symbol in fallback_symbols:
            symbol_data = {
                "symbol": symbol,
                "broker_symbol": symbol,
                "name": f"{symbol} Limited",
                "exchange": "NSE",
                "segment": "EQ",
                "source": "FALLBACK"
            }
            symbols.append(symbol_data)

        logger.info(f"Using fallback list with {len(symbols)} Zerodha symbols")
        return symbols

    def _get_fallback_upstox_symbols(self) -> List[Dict[str, Any]]:
        """Get fallback Upstox symbols"""
        return self._get_fallback_zerodha_symbols()  # Similar format

    def _get_fallback_angel_symbols(self) -> List[Dict[str, Any]]:
        """Get fallback Angel symbols"""
        return self._get_fallback_zerodha_symbols()  # Similar format

    def _get_fallback_iifl_symbols(self) -> List[Dict[str, Any]]:
        """Get fallback IIFL symbols"""
        return self._get_fallback_zerodha_symbols()  # Similar format

    def _get_fallback_nse_symbols(self) -> List[Dict[str, Any]]:
        """Get fallback NSE symbols list"""
        # Popular NSE symbols as fallback
        fallback_symbols = [
            "RELIANCE", "TCS", "HDFCBANK", "ICICIBANK", "INFY", "HINDUNILVR", "ITC",
            "SBIN", "BHARTIARTL", "KOTAKBANK", "LT", "ASIANPAINT", "AXISBANK", "MARUTI",
            "NESTLEIND", "HCLTECH", "WIPRO", "ULTRACEMCO", "BAJFINANCE", "TITAN",
            "SUNPHARMA", "ONGC", "NTPC", "POWERGRID", "COALINDIA", "TATAMOTORS",
            "BAJAJFINSV", "HDFCLIFE", "TECHM", "GRASIM", "ADANIPORTS", "JSWSTEEL",
            "INDUSINDBK", "TATASTEEL", "CIPLA", "DRREDDY", "EICHERMOT", "BRITANNIA",
            "DIVISLAB", "APOLLOHOSP", "BPCL", "HEROMOTOCO", "SHREECEM", "PIDILITIND",
            "BAJAJ-AUTO", "SBILIFE", "ADANIENT", "TATACONSUM", "IOC", "GODREJCP"
        ]

        symbols = []
        for symbol in fallback_symbols:
            symbol_data = {
                "symbol": symbol,
                "name": f"{symbol} Limited",
                "series": "EQ",
                "exchange": "NSE",
                "segment": "EQ",
                "source": "FALLBACK"
            }
            symbols.append(symbol_data)

        logger.info(f"Using fallback list with {len(symbols)} NSE symbols")
        return symbols

    def download_bse_symbols(self) -> List[Dict[str, Any]]:
        """Download BSE symbols"""
        try:
            # BSE equity list URL (alternative approach)
            # Note: BSE doesn't provide a direct CSV like NSE
            # This is a placeholder for BSE symbol download

            logger.info("BSE symbol download not implemented yet")
            return []

        except Exception as e:
            logger.error(f"Error downloading BSE symbols: {e}")
            return []

    def download_fyers_symbols(self) -> List[Dict[str, Any]]:
        """Download Fyers symbol master using multiple methods"""
        try:
            symbols = []

            # Method 1: Try Fyers API if available
            try:
                from broker.fyers_wrapper import FyersBroker
                logger.info("Attempting to download Fyers symbols via API...")

                # Initialize Fyers broker
                fyers_broker = FyersBroker()
                if fyers_broker.fyers:
                    # Get symbols using Fyers API
                    symbols = self._download_fyers_via_api(fyers_broker)
                    if symbols:
                        logger.info(f"Downloaded {len(symbols)} symbols via Fyers API")
                        return symbols

            except Exception as e:
                logger.warning(f"Fyers API method failed: {e}")

            # Method 2: Try downloading from Fyers public URL
            try:
                logger.info("Attempting to download Fyers symbols from public URL...")
                symbols = self._download_fyers_from_url()
                if symbols:
                    logger.info(f"Downloaded {len(symbols)} symbols from Fyers URL")
                    return symbols

            except Exception as e:
                logger.warning(f"Fyers URL method failed: {e}")

            # Method 3: Use fallback Fyers symbols
            logger.warning("All Fyers download methods failed, using fallback symbols")
            return self._get_fallback_fyers_symbols()

        except Exception as e:
            logger.error(f"Error downloading Fyers symbols: {e}")
            return self._get_fallback_fyers_symbols()

    def _download_fyers_via_api(self, fyers_broker) -> List[Dict[str, Any]]:
        """Download symbols using Fyers API"""
        symbols = []

        try:
            # Fyers doesn't have a direct "get all symbols" endpoint
            # But we can get market status and use known symbol lists

            # Get market status to verify API is working
            market_status = fyers_broker._make_api_call("market_status", {})
            if market_status.get('s') != 'ok':
                logger.warning("Fyers API not responding properly")
                return []

            # Use a comprehensive list of popular symbols to get their details
            popular_symbols = [
                "NSE:SBIN-EQ", "NSE:RELIANCE-EQ", "NSE:TCS-EQ", "NSE:HDFCBANK-EQ",
                "NSE:ICICIBANK-EQ", "NSE:INFY-EQ", "NSE:HINDUNILVR-EQ", "NSE:ITC-EQ",
                "NSE:BHARTIARTL-EQ", "NSE:KOTAKBANK-EQ", "NSE:LT-EQ", "NSE:ASIANPAINT-EQ",
                "NSE:AXISBANK-EQ", "NSE:MARUTI-EQ", "NSE:NESTLEIND-EQ", "NSE:HCLTECH-EQ",
                "NSE:WIPRO-EQ", "NSE:ULTRACEMCO-EQ", "NSE:BAJFINANCE-EQ", "NSE:TITAN-EQ",
                "NSE:SUNPHARMA-EQ", "NSE:ONGC-EQ", "NSE:NTPC-EQ", "NSE:POWERGRID-EQ",
                "NSE:COALINDIA-EQ", "NSE:TATAMOTORS-EQ", "NSE:BAJAJFINSV-EQ", "NSE:HDFCLIFE-EQ",
                "NSE:TECHM-EQ", "NSE:GRASIM-EQ", "NSE:ADANIPORTS-EQ", "NSE:JSWSTEEL-EQ",
                "NSE:INDUSINDBK-EQ", "NSE:TATASTEEL-EQ", "NSE:CIPLA-EQ", "NSE:DRREDDY-EQ",
                "NSE:EICHERMOT-EQ", "NSE:BRITANNIA-EQ", "NSE:DIVISLAB-EQ", "NSE:APOLLOHOSP-EQ",
                "NSE:BPCL-EQ", "NSE:HEROMOTOCO-EQ", "NSE:SHREECEM-EQ", "NSE:PIDILITIND-EQ",
                "NSE:BAJAJ-AUTO-EQ", "NSE:SBILIFE-EQ", "NSE:ADANIENT-EQ", "NSE:TATACONSUM-EQ"
            ]

            # Get quotes for these symbols to verify they exist
            for symbol_batch in [popular_symbols[i:i+50] for i in range(0, len(popular_symbols), 50)]:
                try:
                    quotes_data = {"symbols": ",".join(symbol_batch)}
                    quotes_response = fyers_broker._make_api_call("quotes", quotes_data)

                    if quotes_response.get('s') == 'ok' and 'd' in quotes_response:
                        for symbol_data in quotes_response['d']:
                            if symbol_data and 'n' in symbol_data:
                                # Extract symbol information
                                fyers_symbol = symbol_data.get('n', '')
                                if ':' in fyers_symbol and '-' in fyers_symbol:
                                    parts = fyers_symbol.split(':')
                                    if len(parts) == 2:
                                        exchange = parts[0]
                                        symbol_part = parts[1].split('-')[0]

                                        symbol_info = {
                                            "symbol": symbol_part,
                                            "fyers_symbol": fyers_symbol,
                                            "name": f"{symbol_part} Limited",
                                            "exchange": "NSE" if exchange == "NSE" else exchange,
                                            "segment": "EQ",
                                            "source": "FYERS_API",
                                            "ltp": symbol_data.get('lp', 0),
                                            "volume": symbol_data.get('v', 0)
                                        }
                                        symbols.append(symbol_info)

                except Exception as e:
                    logger.debug(f"Error processing symbol batch: {e}")
                    continue

                # Add small delay to respect rate limits
                time.sleep(0.1)

            return symbols

        except Exception as e:
            logger.error(f"Error in Fyers API download: {e}")
            return []

    def _download_fyers_from_url(self) -> List[Dict[str, Any]]:
        """Download symbols from Fyers public URL"""
        symbols = []

        try:
            # Try multiple Fyers symbol URLs
            urls = [
                "https://public.fyers.in/symbols.txt",
                "https://public.fyers.in/symbols.csv",
                "https://api.fyers.in/data-rest/v2/symbols"
            ]

            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Accept": "text/plain,text/csv,application/json,*/*",
                "Connection": "keep-alive"
            }

            for url in urls:
                try:
                    logger.info(f"Trying Fyers URL: {url}")
                    response = requests.get(url, headers=headers, timeout=self.timeout)
                    response.raise_for_status()

                    # Parse response based on content type
                    content = response.text.strip()
                    if not content:
                        continue

                    # Try to parse as different formats
                    if url.endswith('.csv') or ',' in content:
                        symbols = self._parse_fyers_csv(content)
                    elif url.endswith('.txt') or '\n' in content:
                        symbols = self._parse_fyers_txt(content)
                    else:
                        # Try JSON format
                        try:
                            data = response.json()
                            symbols = self._parse_fyers_json(data)
                        except:
                            symbols = self._parse_fyers_txt(content)

                    if symbols:
                        logger.info(f"Successfully parsed {len(symbols)} symbols from {url}")
                        return symbols

                except Exception as e:
                    logger.warning(f"Failed to download from {url}: {e}")
                    continue

            return []

        except Exception as e:
            logger.error(f"Error downloading from Fyers URL: {e}")
            return []

    def _parse_fyers_csv(self, content: str) -> List[Dict[str, Any]]:
        """Parse Fyers CSV format"""
        symbols = []
        try:
            from io import StringIO
            df = pd.read_csv(StringIO(content))

            for _, row in df.iterrows():
                # Adapt to actual CSV structure
                symbol_data = self._create_fyers_symbol_data(row)
                if symbol_data:
                    symbols.append(symbol_data)

        except Exception as e:
            logger.debug(f"Error parsing Fyers CSV: {e}")

        return symbols

    def _parse_fyers_txt(self, content: str) -> List[Dict[str, Any]]:
        """Parse Fyers TXT format (one symbol per line)"""
        symbols = []
        try:
            lines = content.strip().split('\n')
            for line in lines:
                line = line.strip()
                if line and ':' in line and '-' in line:
                    # Parse Fyers symbol format: NSE:SBIN-EQ
                    symbol_data = self._parse_fyers_symbol_line(line)
                    if symbol_data:
                        symbols.append(symbol_data)

        except Exception as e:
            logger.debug(f"Error parsing Fyers TXT: {e}")

        return symbols

    def _parse_fyers_json(self, data: Any) -> List[Dict[str, Any]]:
        """Parse Fyers JSON format"""
        symbols = []
        try:
            if isinstance(data, dict):
                # Handle different JSON structures
                if 'symbols' in data:
                    symbol_list = data['symbols']
                elif 'data' in data:
                    symbol_list = data['data']
                else:
                    symbol_list = data
            else:
                symbol_list = data

            if isinstance(symbol_list, list):
                for item in symbol_list:
                    symbol_data = self._create_fyers_symbol_data(item)
                    if symbol_data:
                        symbols.append(symbol_data)

        except Exception as e:
            logger.debug(f"Error parsing Fyers JSON: {e}")

        return symbols

    def _parse_fyers_symbol_line(self, line: str) -> Optional[Dict[str, Any]]:
        """Parse a single Fyers symbol line"""
        try:
            # Format: NSE:SBIN-EQ or BSE:TCS-EQ
            if ':' in line and '-' in line:
                parts = line.split(':')
                if len(parts) == 2:
                    exchange = parts[0].strip()
                    symbol_part = parts[1].split('-')[0].strip()
                    segment = parts[1].split('-')[1].strip() if '-' in parts[1] else 'EQ'

                    return {
                        "symbol": symbol_part,
                        "fyers_symbol": line,
                        "name": f"{symbol_part} Limited",
                        "exchange": "NSE" if exchange == "NSE" else exchange,
                        "segment": segment,
                        "source": "FYERS_URL"
                    }
        except Exception as e:
            logger.debug(f"Error parsing symbol line {line}: {e}")

        return None

    def _create_fyers_symbol_data(self, row_data: Any) -> Optional[Dict[str, Any]]:
        """Create standardized symbol data from various formats"""
        try:
            if isinstance(row_data, str):
                return self._parse_fyers_symbol_line(row_data)
            elif isinstance(row_data, dict):
                # Handle dictionary format
                symbol = row_data.get('symbol', '')
                fyers_symbol = row_data.get('fyers_symbol', symbol)

                if fyers_symbol and ':' in fyers_symbol:
                    return self._parse_fyers_symbol_line(fyers_symbol)
            elif hasattr(row_data, 'to_dict'):
                # Handle pandas Series
                return self._create_fyers_symbol_data(row_data.to_dict())

        except Exception as e:
            logger.debug(f"Error creating symbol data: {e}")

        return None

    def _get_fallback_fyers_symbols(self) -> List[Dict[str, Any]]:
        """Get fallback Fyers symbols list"""
        # Popular Fyers symbols as fallback
        fallback_symbols = [
            "NSE:RELIANCE-EQ", "NSE:TCS-EQ", "NSE:HDFCBANK-EQ", "NSE:ICICIBANK-EQ",
            "NSE:INFY-EQ", "NSE:HINDUNILVR-EQ", "NSE:ITC-EQ", "NSE:SBIN-EQ",
            "NSE:BHARTIARTL-EQ", "NSE:KOTAKBANK-EQ", "NSE:LT-EQ", "NSE:ASIANPAINT-EQ",
            "NSE:AXISBANK-EQ", "NSE:MARUTI-EQ", "NSE:NESTLEIND-EQ", "NSE:HCLTECH-EQ",
            "NSE:WIPRO-EQ", "NSE:ULTRACEMCO-EQ", "NSE:BAJFINANCE-EQ", "NSE:TITAN-EQ",
            "NSE:SUNPHARMA-EQ", "NSE:ONGC-EQ", "NSE:NTPC-EQ", "NSE:POWERGRID-EQ",
            "NSE:COALINDIA-EQ", "NSE:TATAMOTORS-EQ", "NSE:BAJAJFINSV-EQ", "NSE:HDFCLIFE-EQ",
            "NSE:TECHM-EQ", "NSE:GRASIM-EQ", "NSE:ADANIPORTS-EQ", "NSE:JSWSTEEL-EQ",
            "NSE:INDUSINDBK-EQ", "NSE:TATASTEEL-EQ", "NSE:CIPLA-EQ", "NSE:DRREDDY-EQ",
            "NSE:EICHERMOT-EQ", "NSE:BRITANNIA-EQ", "NSE:DIVISLAB-EQ", "NSE:APOLLOHOSP-EQ",
            "NSE:BPCL-EQ", "NSE:HEROMOTOCO-EQ", "NSE:SHREECEM-EQ", "NSE:PIDILITIND-EQ",
            "NSE:BAJAJ-AUTO-EQ", "NSE:SBILIFE-EQ", "NSE:ADANIENT-EQ", "NSE:TATACONSUM-EQ"
        ]

        symbols = []
        for fyers_symbol in fallback_symbols:
            symbol_data = self._parse_fyers_symbol_line(fyers_symbol)
            if symbol_data:
                symbol_data["source"] = "FALLBACK"
                symbols.append(symbol_data)

        logger.info(f"Using fallback list with {len(symbols)} Fyers symbols")
        return symbols

    def save_symbols(self, symbols: List[Dict[str, Any]], exchange: str) -> bool:
        """Save symbols to file"""
        try:
            if exchange.upper() == "NSE":
                file_path = self.nse_symbols_file
            elif exchange.upper() == "BSE":
                file_path = self.bse_symbols_file
            elif exchange.upper() == "FYERS":
                file_path = self.fyers_symbols_file
            else:
                logger.error(f"Unknown exchange: {exchange}")
                return False

            # Create backup if file exists
            if file_path.exists():
                backup_path = file_path.with_suffix(f".bak.{datetime.now().strftime('%Y%m%d_%H%M%S')}")
                file_path.rename(backup_path)
                logger.info(f"Created backup: {backup_path}")

            # Save symbols
            data = {
                "exchange": exchange.upper(),
                "symbols": symbols,
                "count": len(symbols),
                "updated_at": datetime.now().isoformat(),
                "version": "1.0"
            }

            with open(file_path, 'w') as f:
                json.dump(data, f, indent=2)

            logger.info(f"Saved {len(symbols)} {exchange} symbols to {file_path}")
            return True

        except Exception as e:
            logger.error(f"Error saving {exchange} symbols: {e}")
            return False

    def load_symbols(self, exchange: str) -> List[Dict[str, Any]]:
        """Load symbols from file"""
        try:
            if exchange.upper() == "NSE":
                file_path = self.nse_symbols_file
            elif exchange.upper() == "BSE":
                file_path = self.bse_symbols_file
            elif exchange.upper() == "FYERS":
                file_path = self.fyers_symbols_file
            else:
                logger.error(f"Unknown exchange: {exchange}")
                return []

            if not file_path.exists():
                logger.warning(f"Symbol file not found: {file_path}")
                return []

            with open(file_path, 'r') as f:
                data = json.load(f)

            symbols = data.get("symbols", [])
            logger.info(f"Loaded {len(symbols)} {exchange} symbols")
            return symbols

        except Exception as e:
            logger.error(f"Error loading {exchange} symbols: {e}")
            return []

    def update_symbols(self, exchange: str = None) -> Dict[str, int]:
        """Update symbols for specified exchange or all exchanges"""
        results = {}

        exchanges = [exchange] if exchange else ["NSE", "BSE", "FYERS"]

        for exch in exchanges:
            try:
                if exch.upper() == "NSE":
                    symbols = self.download_nse_symbols()
                elif exch.upper() == "BSE":
                    symbols = self.download_bse_symbols()
                elif exch.upper() == "FYERS":
                    symbols = self.download_fyers_symbols()
                else:
                    continue

                if symbols:
                    if self.save_symbols(symbols, exch):
                        results[exch] = len(symbols)
                        self._last_update[exch] = datetime.now()
                    else:
                        results[exch] = 0
                else:
                    results[exch] = 0

            except Exception as e:
                logger.error(f"Error updating {exch} symbols: {e}")
                results[exch] = 0

        return results

    def create_master_contract(self) -> bool:
        """Create master contract file combining all exchanges"""
        try:
            master_contract = {
                "created_at": datetime.now().isoformat(),
                "version": "1.0",
                "exchanges": {},
                "total_symbols": 0
            }

            total_symbols = 0

            # Load symbols from all exchanges
            for exchange in ["NSE", "BSE", "FYERS"]:
                symbols = self.load_symbols(exchange)
                if symbols:
                    master_contract["exchanges"][exchange] = {
                        "count": len(symbols),
                        "symbols": symbols,
                        "last_updated": self._last_update.get(exchange, datetime.now()).isoformat()
                    }
                    total_symbols += len(symbols)

            master_contract["total_symbols"] = total_symbols

            # Save master contract
            with open(self.master_contract_file, 'w') as f:
                json.dump(master_contract, f, indent=2)

            logger.info(f"Created master contract with {total_symbols} symbols")
            return True

        except Exception as e:
            logger.error(f"Error creating master contract: {e}")
            return False

    def get_symbol_info(self, symbol: str, exchange: str = None) -> Optional[Dict[str, Any]]:
        """Get information for a specific symbol"""
        try:
            exchanges = [exchange] if exchange else ["NSE", "BSE", "FYERS"]

            for exch in exchanges:
                symbols = self.load_symbols(exch)
                for symbol_data in symbols:
                    if symbol_data.get("symbol", "").upper() == symbol.upper():
                        return symbol_data

            return None

        except Exception as e:
            logger.error(f"Error getting symbol info for {symbol}: {e}")
            return None

    def search_symbols(self, query: str, exchange: str = None, limit: int = 50) -> List[Dict[str, Any]]:
        """Search symbols by name or symbol"""
        try:
            results = []
            query = query.upper()

            exchanges = [exchange] if exchange else ["NSE", "BSE", "FYERS"]

            for exch in exchanges:
                symbols = self.load_symbols(exch)
                for symbol_data in symbols:
                    # Handle different symbol data formats
                    if isinstance(symbol_data, dict):
                        symbol = symbol_data.get("symbol", "").upper()
                        name = symbol_data.get("name", "").upper()
                    elif isinstance(symbol_data, str):
                        # Handle simple string symbols (like BSE format)
                        symbol = symbol_data.upper()
                        name = ""
                        # Convert to dict format
                        symbol_data = {
                            "symbol": symbol_data,
                            "name": f"BSE {symbol_data}",
                            "exchange": exch,
                            "segment": "EQ"
                        }
                    else:
                        continue

                    if query in symbol or query in name:
                        results.append(symbol_data)

                        if len(results) >= limit:
                            break

                if len(results) >= limit:
                    break

            return results

        except Exception as e:
            logger.error(f"Error searching symbols: {e}")
            return []

    def is_update_needed(self, exchange: str) -> bool:
        """Check if symbol update is needed for an exchange"""
        try:
            last_update = self._last_update.get(exchange)
            if not last_update:
                return True

            time_diff = datetime.now() - last_update
            return time_diff.total_seconds() > (self.update_interval_hours * 3600)

        except Exception as e:
            logger.error(f"Error checking update status: {e}")
            return True

    def schedule_daily_updates(self):
        """Schedule daily symbol updates"""
        try:
            # Schedule updates at 6 AM daily
            schedule.every().day.at("06:00").do(self.daily_update_job)

            logger.info("Scheduled daily symbol updates at 6:00 AM")

            # Run scheduler in background
            while True:
                schedule.run_pending()
                time.sleep(60)  # Check every minute

        except KeyboardInterrupt:
            logger.info("Symbol update scheduler stopped")
        except Exception as e:
            logger.error(f"Error in scheduler: {e}")

    def daily_update_job(self):
        """Daily update job"""
        try:
            logger.info("Starting daily symbol update...")

            results = self.update_symbols()

            # Create master contract
            self.create_master_contract()

            # Log results
            total_updated = sum(results.values())
            logger.info(f"Daily update completed: {total_updated} symbols updated")

            for exchange, count in results.items():
                logger.info(f"{exchange}: {count} symbols")

        except Exception as e:
            logger.error(f"Error in daily update job: {e}")


    # New broker-specific methods
    def load_broker_symbols(self, broker_name: str) -> List[Dict[str, Any]]:
        """Load symbols for a specific broker"""
        try:
            broker_dir = self.brokers_dir / broker_name.lower()
            symbol_file = broker_dir / f"{broker_name.lower()}_symbols.json"

            if not symbol_file.exists():
                logger.warning(f"Symbol file not found for broker: {broker_name}")
                return []

            with open(symbol_file, 'r') as f:
                data = json.load(f)

            symbols = data.get("symbols", [])
            logger.info(f"Loaded {len(symbols)} symbols for broker: {broker_name}")
            return symbols

        except Exception as e:
            logger.error(f"Error loading symbols for broker {broker_name}: {e}")
            return []

    def get_broker_symbol_mapping(self, rapidtrader_symbol: str, broker_name: str) -> Optional[str]:
        """Get broker-specific symbol for a RapidTrader symbol"""
        try:
            if not self.unified_mapping_file.exists():
                logger.warning("Unified mapping file not found")
                return None

            with open(self.unified_mapping_file, 'r') as f:
                unified_mapping = json.load(f)

            symbol_data = unified_mapping.get("unified_symbols", {}).get(rapidtrader_symbol.upper(), {})
            broker_data = symbol_data.get("brokers", {}).get(broker_name.lower(), {})

            return broker_data.get("broker_symbol")

        except Exception as e:
            logger.error(f"Error getting broker symbol mapping: {e}")
            return None

    def search_broker_symbols(self, query: str, broker_name: str = None, limit: int = 50) -> List[Dict[str, Any]]:
        """Search symbols across brokers or specific broker"""
        try:
            results = []
            query = query.upper()

            if broker_name:
                # Search specific broker
                symbols = self.load_broker_symbols(broker_name)
                for symbol_data in symbols:
                    symbol = symbol_data.get("symbol", "").upper()
                    name = symbol_data.get("name", "").upper()

                    if query in symbol or query in name:
                        results.append(symbol_data)
                        if len(results) >= limit:
                            break
            else:
                # Search all brokers
                for broker in self.broker_configs.keys():
                    symbols = self.load_broker_symbols(broker)
                    for symbol_data in symbols:
                        symbol = symbol_data.get("symbol", "").upper()
                        name = symbol_data.get("name", "").upper()

                        if query in symbol or query in name:
                            symbol_data["broker"] = broker  # Add broker info
                            results.append(symbol_data)
                            if len(results) >= limit:
                                break

                    if len(results) >= limit:
                        break

            return results

        except Exception as e:
            logger.error(f"Error searching broker symbols: {e}")
            return []

    def create_broker_master_contract(self) -> bool:
        """Create master contract combining all broker symbols"""
        try:
            master_contract = {
                "created_at": datetime.now().isoformat(),
                "version": "2.0",
                "brokers": {},
                "total_symbols": 0,
                "total_brokers": 0
            }

            total_symbols = 0

            # Load symbols from all registered brokers
            for broker_name, broker_info in self.broker_configs.items():
                symbols = self.load_broker_symbols(broker_name)
                if symbols:
                    master_contract["brokers"][broker_name] = {
                        "count": len(symbols),
                        "symbols": symbols,
                        "last_updated": broker_info.get("last_symbol_update"),
                        "status": broker_info.get("status", "unknown"),
                        "config_registered": broker_info.get("registered_at")
                    }
                    total_symbols += len(symbols)

            master_contract["total_symbols"] = total_symbols
            master_contract["total_brokers"] = len(master_contract["brokers"])

            # Save master contract
            with open(self.master_contract_file, 'w') as f:
                json.dump(master_contract, f, indent=2)

            logger.info(f"Created broker master contract with {total_symbols} symbols from {len(master_contract['brokers'])} brokers")
            return True

        except Exception as e:
            logger.error(f"Error creating broker master contract: {e}")
            return False

    def get_broker_status(self) -> Dict[str, Any]:
        """Get status of all registered brokers"""
        try:
            status = {
                "total_brokers": len(self.broker_configs),
                "brokers": {},
                "summary": {
                    "active": 0,
                    "error": 0,
                    "registered": 0
                }
            }

            for broker_name, broker_info in self.broker_configs.items():
                broker_status = broker_info.get("status", "unknown")
                symbol_count = broker_info.get("symbol_count", 0)
                last_update = broker_info.get("last_symbol_update")

                status["brokers"][broker_name] = {
                    "status": broker_status,
                    "symbol_count": symbol_count,
                    "last_update": last_update,
                    "registered_at": broker_info.get("registered_at")
                }

                # Update summary
                if broker_status in status["summary"]:
                    status["summary"][broker_status] += 1

            return status

        except Exception as e:
            logger.error(f"Error getting broker status: {e}")
            return {}


def main():
    """Main function for CLI usage"""
    import argparse

    parser = argparse.ArgumentParser(description="RapidTrader Broker Symbol Manager")

    # Broker-specific commands
    parser.add_argument("--register-broker", help="Register a new broker (format: broker_name)")
    parser.add_argument("--update-broker", help="Update symbols for specific broker")
    parser.add_argument("--list-brokers", action="store_true", help="List all registered brokers")
    parser.add_argument("--broker-status", action="store_true", help="Show broker status")

    # Symbol operations
    parser.add_argument("--search", help="Search symbols")
    parser.add_argument("--broker", help="Specify broker for search/operations")
    parser.add_argument("--info", help="Get symbol information")
    parser.add_argument("--mapping", help="Get broker symbol mapping for RapidTrader symbol")

    # Legacy support
    parser.add_argument("--update", choices=["NSE", "BSE", "FYERS", "ALL"],
                       help="Update symbols for specific exchange (legacy)")
    parser.add_argument("--exchange", choices=["NSE", "BSE", "FYERS"],
                       help="Specify exchange (legacy)")

    # Utility commands
    parser.add_argument("--master-contract", action="store_true",
                       help="Create master contract file")
    parser.add_argument("--schedule", action="store_true",
                       help="Start daily update scheduler")

    args = parser.parse_args()

    manager = BrokerSymbolManager()

    if args.register_broker:
        # Demo broker registration (in real usage, this would come from config)
        broker_name = args.register_broker.lower()
        demo_config = {
            "client_id": "demo_client_id",
            "access_token": "demo_access_token",
            "dry_run": True
        }

        success = manager.register_broker_config(broker_name, demo_config)
        if success:
            print(f"✅ Successfully registered broker: {broker_name}")
        else:
            print(f"❌ Failed to register broker: {broker_name}")

    elif args.update_broker:
        broker_name = args.update_broker.lower()
        success = manager.update_broker_symbols(broker_name)
        if success:
            print(f"✅ Successfully updated symbols for broker: {broker_name}")
        else:
            print(f"❌ Failed to update symbols for broker: {broker_name}")

    elif args.list_brokers:
        brokers = list(manager.broker_configs.keys())
        if brokers:
            print(f"📋 Registered brokers ({len(brokers)}):")
            for broker in brokers:
                status = manager.broker_configs[broker].get("status", "unknown")
                count = manager.broker_configs[broker].get("symbol_count", 0)
                print(f"  • {broker}: {status} ({count} symbols)")
        else:
            print("📋 No brokers registered")

    elif args.broker_status:
        status = manager.get_broker_status()
        print(f"📊 Broker Status Summary:")
        print(f"  Total Brokers: {status.get('total_brokers', 0)}")
        print(f"  Active: {status.get('summary', {}).get('active', 0)}")
        print(f"  Error: {status.get('summary', {}).get('error', 0)}")
        print(f"  Registered: {status.get('summary', {}).get('registered', 0)}")

        print(f"\n📋 Detailed Status:")
        for broker, info in status.get('brokers', {}).items():
            print(f"  • {broker}: {info['status']} ({info['symbol_count']} symbols)")

    elif args.search:
        results = manager.search_broker_symbols(args.search, args.broker)
        print(f"🔍 Found {len(results)} symbols:")
        for symbol in results[:10]:  # Show first 10
            broker_info = f" [{symbol.get('broker', 'unknown')}]" if 'broker' in symbol else ""
            print(f"  • {symbol.get('symbol')} - {symbol.get('name')} ({symbol.get('exchange')}){broker_info}")

    elif args.mapping:
        if args.broker:
            mapping = manager.get_broker_symbol_mapping(args.mapping, args.broker)
            if mapping:
                print(f"🔗 {args.mapping} → {mapping} ({args.broker})")
            else:
                print(f"❌ No mapping found for {args.mapping} in {args.broker}")
        else:
            print("❌ Please specify --broker for symbol mapping")

    elif args.master_contract:
        if manager.create_broker_master_contract():
            print("✅ Broker master contract created successfully")
        else:
            print("❌ Failed to create broker master contract")

    elif args.update:
        # Legacy support
        print("⚠️  Legacy mode: Use --update-broker instead")
        exchange = None if args.update == "ALL" else args.update
        # Convert to broker name for legacy support
        if exchange == "FYERS":
            success = manager.update_broker_symbols("fyers")
            print(f"Update result for Fyers: {'✅ Success' if success else '❌ Failed'}")
        else:
            print(f"Legacy exchange {exchange} not supported in broker mode")

    elif args.schedule:
        print("🕐 Starting daily update scheduler...")
        manager.schedule_daily_updates()

    else:
        parser.print_help()
        print(f"\n📖 Examples:")
        print(f"  Register Fyers:     python {sys.argv[0]} --register-broker fyers")
        print(f"  Update Fyers:       python {sys.argv[0]} --update-broker fyers")
        print(f"  List brokers:       python {sys.argv[0]} --list-brokers")
        print(f"  Search symbols:     python {sys.argv[0]} --search RELIANCE")
        print(f"  Search in Fyers:    python {sys.argv[0]} --search RELIANCE --broker fyers")
        print(f"  Get mapping:        python {sys.argv[0]} --mapping RELIANCE --broker fyers")
        print(f"  Master contract:    python {sys.argv[0]} --master-contract")


if __name__ == "__main__":
    main()
