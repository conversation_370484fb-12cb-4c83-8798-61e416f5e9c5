# Optimized requirements for RapidTrader - Production minimal dependencies
# Core trading and data dependencies only

# Core Python packages
numpy==1.26.4
pandas==2.2.3
python-dateutil==2.9.0.post0
pytz==2025.2
tzdata==2025.2

# Broker APIs - Essential only
dhanhq==2.0.2
fyers_apiv3==3.1.7

# Data fetching - Minimal
yfinance==0.2.61
requests==2.31.0
urllib3==2.4.0

# Technical analysis - Lightweight
pandas_ta==0.3.14b0

# Web framework - Minimal
flask==3.0.0
flask-cors==4.0.0

# Async and networking
aiohttp==3.9.3
websocket-client==1.6.1
websockets==15.0.1

# Configuration and utilities
python-dotenv==1.1.0
PyYAML==6.0.2
click==8.2.0

# Security
cryptography==45.0.2
certifi==2025.4.26

# Database - Lightweight
peewee==3.18.1

# System utilities
psutil==7.0.0

# Removed heavy dependencies:
# - aws-lambda-powertools (not needed for core trading)
# - boto3/botocore (AWS not essential)
# - beautifulsoup4 (web scraping not core)
# - matplotlib (plotting can be optional)
# - rich (fancy output not essential)
# - ta-lib (using pandas_ta instead)
