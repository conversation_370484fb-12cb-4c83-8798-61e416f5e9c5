#!/bin/bash

# RapidTrader Optimized Docker Build Script
# Builds ultra-fast, minimal Docker images for production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    printf "${GREEN}[$(date +'%H:%M:%S')] %s${NC}\n" "$1"
}

error() {
    printf "${RED}[$(date +'%H:%M:%S')] ERROR: %s${NC}\n" "$1" >&2
}

warn() {
    printf "${YELLOW}[$(date +'%H:%M:%S')] WARNING: %s${NC}\n" "$1"
}

info() {
    printf "${BLUE}[$(date +'%H:%M:%S')] INFO: %s${NC}\n" "$1"
}

# Configuration
REGISTRY=${REGISTRY:-""}
TAG=${TAG:-"optimized"}
PUSH=${PUSH:-false}
CACHE=${CACHE:-true}
PARALLEL=${PARALLEL:-true}

# Image names
RAPIDTRADER_IMAGE="rapidtrader:${TAG}"
API_IMAGE="rapidtrader-api:${TAG}"
FRONTEND_IMAGE="rapidtrader-frontend:${TAG}"

# Build arguments
BUILD_ARGS=(
    "--build-arg" "BUILDKIT_INLINE_CACHE=1"
    "--build-arg" "PYTHON_VERSION=3.11"
    "--build-arg" "ALPINE_VERSION=3.18"
)

if [ "$CACHE" = "true" ]; then
    BUILD_ARGS+=("--cache-from" "${RAPIDTRADER_IMAGE}")
fi

# Function to check Docker and BuildKit
check_requirements() {
    log "Checking requirements..."

    if ! command -v docker >/dev/null 2>&1; then
        error "Docker not found. Please install Docker."
        exit 1
    fi

    # Check if bc is available for calculations
    if ! command -v bc >/dev/null 2>&1; then
        warn "bc command not found. Installing..."
        # Try to install bc if possible
        if command -v apt-get >/dev/null 2>&1; then
            sudo apt-get update && sudo apt-get install -y bc >/dev/null 2>&1 || warn "Could not install bc"
        elif command -v yum >/dev/null 2>&1; then
            sudo yum install -y bc >/dev/null 2>&1 || warn "Could not install bc"
        elif command -v pacman >/dev/null 2>&1; then
            sudo pacman -S --noconfirm bc >/dev/null 2>&1 || warn "Could not install bc"
        fi
    fi

    # Try to enable BuildKit for faster builds
    export DOCKER_BUILDKIT=1
    export COMPOSE_DOCKER_CLI_BUILD=1

    # Check if BuildKit is available
    if docker buildx version >/dev/null 2>&1; then
        info "Using Docker BuildKit for optimized builds"
    else
        warn "Docker BuildKit not available. Using standard build."
        unset DOCKER_BUILDKIT
        unset COMPOSE_DOCKER_CLI_BUILD
    fi
}

# Function to clean up old images
cleanup_old_images() {
    log "Cleaning up old images..."

    # Remove dangling images
    docker image prune -f >/dev/null 2>&1 || true

    # Remove old rapidtrader images (keep last 3)
    docker images --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}\t{{.ID}}" | \
        grep "rapidtrader" | \
        tail -n +4 | \
        awk '{print $3}' | \
        xargs -r docker rmi >/dev/null 2>&1 || true

    info "Cleanup completed"
}

# Function to build main RapidTrader image
build_rapidtrader() {
    log "Building optimized RapidTrader image..."

    local start_time=$(date +%s)

    # Copy optimized requirements if it exists
    if [ -f "requirements.optimized.txt" ]; then
        cp requirements.optimized.txt requirements.txt.bak
        cp requirements.optimized.txt requirements.txt
        info "Using optimized requirements"
    fi

    # Build with optimizations
    docker build \
        -f Dockerfile.alpine \
        -t "${RAPIDTRADER_IMAGE}" \
        "${BUILD_ARGS[@]}" \
        --target production \
        --label "build.date=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
        --label "build.version=${TAG}" \
        --label "build.optimized=true" \
        . || {
            error "Failed to build RapidTrader image"
            # Restore original requirements
            [ -f "requirements.txt.bak" ] && mv requirements.txt.bak requirements.txt
            exit 1
        }

    # Restore original requirements
    [ -f "requirements.txt.bak" ] && mv requirements.txt.bak requirements.txt

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    # Get image size
    local size=$(docker images "${RAPIDTRADER_IMAGE}" --format "table {{.Size}}" | tail -n 1)

    log "RapidTrader image built successfully in ${duration}s (Size: ${size})"
}

# Function to build API Gateway image
build_api_gateway() {
    log "Building optimized API Gateway image..."

    local start_time=$(date +%s)

    docker build \
        -f api_gateway/Dockerfile \
        -t "${API_IMAGE}" \
        "${BUILD_ARGS[@]}" \
        --label "build.date=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
        --label "build.version=${TAG}" \
        --label "build.optimized=true" \
        . || {
            error "Failed to build API Gateway image"
            exit 1
        }

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    local size=$(docker images "${API_IMAGE}" --format "table {{.Size}}" | tail -n 1)

    log "API Gateway image built successfully in ${duration}s (Size: ${size})"
}

# Function to build Frontend image
build_frontend() {
    log "Building optimized Frontend image..."

    local start_time=$(date +%s)

    # Check if frontend_v2 exists, fallback to frontend
    local frontend_dir="frontend_v2"
    [ ! -d "$frontend_dir" ] && frontend_dir="frontend"

    if [ ! -d "$frontend_dir" ]; then
        warn "No frontend directory found, skipping frontend build"
        return 0
    fi

    docker build \
        -f "${frontend_dir}/Dockerfile" \
        -t "${FRONTEND_IMAGE}" \
        "${BUILD_ARGS[@]}" \
        --target production \
        --label "build.date=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
        --label "build.version=${TAG}" \
        --label "build.optimized=true" \
        "${frontend_dir}" || {
            error "Failed to build Frontend image"
            exit 1
        }

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    local size=$(docker images "${FRONTEND_IMAGE}" --format "table {{.Size}}" | tail -n 1)

    log "Frontend image built successfully in ${duration}s (Size: ${size})"
}

# Function to build all images in parallel
build_parallel() {
    log "Building all images in parallel..."

    # Build RapidTrader first (others depend on it)
    build_rapidtrader

    # Build API and Frontend in parallel
    {
        build_api_gateway &
        build_frontend &
        wait
    }
}

# Function to build all images sequentially
build_sequential() {
    log "Building all images sequentially..."

    build_rapidtrader
    build_api_gateway
    build_frontend
}

# Function to tag images for registry
tag_for_registry() {
    if [ -n "$REGISTRY" ]; then
        log "Tagging images for registry: $REGISTRY"

        docker tag "${RAPIDTRADER_IMAGE}" "${REGISTRY}/${RAPIDTRADER_IMAGE}"
        docker tag "${API_IMAGE}" "${REGISTRY}/${API_IMAGE}"
        docker tag "${FRONTEND_IMAGE}" "${REGISTRY}/${FRONTEND_IMAGE}"

        info "Images tagged for registry"
    fi
}

# Function to push images to registry
push_images() {
    if [ "$PUSH" = "true" ] && [ -n "$REGISTRY" ]; then
        log "Pushing images to registry..."

        docker push "${REGISTRY}/${RAPIDTRADER_IMAGE}" &
        docker push "${REGISTRY}/${API_IMAGE}" &
        docker push "${REGISTRY}/${FRONTEND_IMAGE}" &
        wait

        log "Images pushed successfully"
    fi
}

# Function to display build summary
show_summary() {
    log "Build Summary:"
    echo ""

    # Show image sizes
    printf "%-30s %-15s %-20s\n" "IMAGE" "SIZE" "CREATED"
    printf "%-30s %-15s %-20s\n" "-----" "----" "-------"

    for image in "${RAPIDTRADER_IMAGE}" "${API_IMAGE}" "${FRONTEND_IMAGE}"; do
        if docker images "$image" --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}\t{{.CreatedSince}}" | tail -n 1 | grep -q "$image"; then
            docker images "$image" --format "%-30s %-15s %-20s" | tail -n 1
        fi
    done

    echo ""

    # Show total size
    local total_size=$(docker images "${RAPIDTRADER_IMAGE}" "${API_IMAGE}" "${FRONTEND_IMAGE}" --format "{{.Size}}" | \
        sed 's/MB//' | sed 's/GB/*1000/' | bc 2>/dev/null | \
        awk '{sum+=$1} END {print sum "MB"}' 2>/dev/null || echo "N/A")

    info "Total size: $total_size"

    # Show optimization tips
    echo ""
    info "Optimization tips:"
    echo "  - Use 'docker system prune' to clean up build cache"
    echo "  - Use 'docker-compose -f docker-compose.optimized.yml' for deployment"
    echo "  - Monitor container resources with 'docker stats'"
}

# Main execution
main() {
    log "🚀 RapidTrader Optimized Build Script"

    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --registry)
                REGISTRY="$2"
                shift 2
                ;;
            --tag)
                TAG="$2"
                shift 2
                ;;
            --push)
                PUSH=true
                shift
                ;;
            --no-cache)
                CACHE=false
                shift
                ;;
            --sequential)
                PARALLEL=false
                shift
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --registry REGISTRY   Docker registry to push to"
                echo "  --tag TAG            Image tag (default: optimized)"
                echo "  --push               Push images to registry"
                echo "  --no-cache           Disable build cache"
                echo "  --sequential         Build images sequentially"
                echo "  --help               Show this help"
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                exit 1
                ;;
        esac
    done

    # Check requirements
    check_requirements

    # Clean up old images
    cleanup_old_images

    # Build images
    if [ "$PARALLEL" = "true" ]; then
        build_parallel
    else
        build_sequential
    fi

    # Tag for registry
    tag_for_registry

    # Push to registry
    push_images

    # Show summary
    show_summary

    log "✅ Build completed successfully!"
}

# Execute main function
main "$@"
