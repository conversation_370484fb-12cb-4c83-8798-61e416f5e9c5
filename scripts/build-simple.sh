#!/bin/bash

# Simplified RapidTrader Optimized Build Script
# Works with legacy Docker builder and doesn't require additional tools

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    printf "${GREEN}[$(date +'%H:%M:%S')] %s${NC}\n" "$1"
}

error() {
    printf "${RED}[$(date +'%H:%M:%S')] ERROR: %s${NC}\n" "$1" >&2
}

warn() {
    printf "${YELLOW}[$(date +'%H:%M:%S')] WARNING: %s${NC}\n" "$1"
}

info() {
    printf "${BLUE}[$(date +'%H:%M:%S')] INFO: %s${NC}\n" "$1"
}

# Configuration
TAG=${TAG:-"optimized"}
RAPIDTRADER_IMAGE="rapidtrader:${TAG}"

# Function to check Docker
check_requirements() {
    log "Checking requirements..."
    
    if ! command -v docker >/dev/null 2>&1; then
        error "Docker not found. Please install Docker."
        exit 1
    fi
    
    info "Docker found: $(docker --version)"
}

# Function to clean up old images
cleanup_old_images() {
    log "Cleaning up old images..."
    
    # Remove dangling images
    docker image prune -f >/dev/null 2>&1 || true
    
    info "Cleanup completed"
}

# Function to build main RapidTrader image
build_rapidtrader() {
    log "Building optimized RapidTrader image..."
    
    local start_time=$(date +%s)
    
    # Copy optimized requirements if it exists
    if [ -f "requirements.optimized.txt" ]; then
        cp requirements.txt requirements.txt.backup 2>/dev/null || true
        cp requirements.optimized.txt requirements.txt
        info "Using optimized requirements"
    fi
    
    # Build with optimizations
    docker build \
        -f Dockerfile.alpine \
        -t "${RAPIDTRADER_IMAGE}" \
        --label "build.date=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
        --label "build.version=${TAG}" \
        --label "build.optimized=true" \
        . || {
            error "Failed to build RapidTrader image"
            # Restore original requirements
            [ -f "requirements.txt.backup" ] && mv requirements.txt.backup requirements.txt
            exit 1
        }
    
    # Restore original requirements
    [ -f "requirements.txt.backup" ] && mv requirements.txt.backup requirements.txt
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # Get image size
    local size=$(docker images "${RAPIDTRADER_IMAGE}" --format "{{.Size}}" | head -1)
    
    log "RapidTrader image built successfully in ${duration}s (Size: ${size})"
}

# Function to display build summary
show_summary() {
    log "Build Summary:"
    echo ""
    
    # Show image information
    printf "%-30s %-15s %-20s\n" "IMAGE" "SIZE" "CREATED"
    printf "%-30s %-15s %-20s\n" "-----" "----" "-------"
    
    if docker images "$RAPIDTRADER_IMAGE" --format "{{.Repository}}:{{.Tag}}" | grep -q "$RAPIDTRADER_IMAGE"; then
        docker images "$RAPIDTRADER_IMAGE" --format "%-30s %-15s %-20s"
    fi
    
    echo ""
    
    info "Build completed successfully!"
    echo ""
    info "Usage examples:"
    echo "  docker run ${RAPIDTRADER_IMAGE} help"
    echo "  docker run ${RAPIDTRADER_IMAGE} backtest --optimized"
    echo "  docker run ${RAPIDTRADER_IMAGE} dryrun --optimized"
    echo ""
    info "For deployment:"
    echo "  docker-compose -f docker-compose.optimized.yml up"
}

# Function to test the built image
test_image() {
    log "Testing built image..."
    
    # Test basic functionality
    if docker run --rm "${RAPIDTRADER_IMAGE}" help >/dev/null 2>&1; then
        info "✅ Image test passed - basic functionality works"
    else
        error "❌ Image test failed - basic functionality broken"
        return 1
    fi
    
    # Test Python imports
    if docker run --rm "${RAPIDTRADER_IMAGE}" python -c "import core.rapidtrader; print('Core imports OK')" >/dev/null 2>&1; then
        info "✅ Python imports test passed"
    else
        error "❌ Python imports test failed"
        return 1
    fi
    
    info "All tests passed!"
}

# Main execution
main() {
    log "🚀 RapidTrader Simplified Build Script"
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --tag)
                TAG="$2"
                RAPIDTRADER_IMAGE="rapidtrader:${TAG}"
                shift 2
                ;;
            --test)
                TEST_IMAGE=true
                shift
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --tag TAG            Image tag (default: optimized)"
                echo "  --test               Test the built image"
                echo "  --help               Show this help"
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Check requirements
    check_requirements
    
    # Clean up old images
    cleanup_old_images
    
    # Build image
    build_rapidtrader
    
    # Test image if requested
    if [ "$TEST_IMAGE" = "true" ]; then
        test_image
    fi
    
    # Show summary
    show_summary
    
    log "✅ Build completed successfully!"
}

# Execute main function
main "$@"
