#!/bin/bash

# RapidTrader Project Cleanup Script
# Removes unnecessary files and directories to optimize the project

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    printf "${GREEN}[$(date +'%H:%M:%S')] %s${NC}\n" "$1"
}

error() {
    printf "${RED}[$(date +'%H:%M:%S')] ERROR: %s${NC}\n" "$1" >&2
}

warn() {
    printf "${YELLOW}[$(date +'%H:%M:%S')] WARNING: %s${NC}\n" "$1"
}

info() {
    printf "${BLUE}[$(date +'%H:%M:%S')] INFO: %s${NC}\n" "$1"
}

# Function to calculate directory size
get_size() {
    if [ -d "$1" ] || [ -f "$1" ]; then
        du -sh "$1" 2>/dev/null | cut -f1 || echo "0"
    else
        echo "0"
    fi
}

# Function to safely remove files/directories
safe_remove() {
    local target="$1"
    local description="$2"
    
    if [ -e "$target" ]; then
        local size=$(get_size "$target")
        rm -rf "$target"
        info "Removed $description ($size)"
    fi
}

# Function to clean Python cache files
clean_python_cache() {
    log "Cleaning Python cache files..."
    
    # Remove __pycache__ directories
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    
    # Remove .pyc files
    find . -name "*.pyc" -delete 2>/dev/null || true
    
    # Remove .pyo files
    find . -name "*.pyo" -delete 2>/dev/null || true
    
    info "Python cache files cleaned"
}

# Function to remove unnecessary documentation
clean_documentation() {
    log "Cleaning unnecessary documentation..."
    
    # Keep only essential docs
    safe_remove "docs/API_ARCHITECTURE.md" "API Architecture docs"
    safe_remove "docs/BACKTESTING_ARCHITECTURE.md" "Backtesting Architecture docs"
    safe_remove "docs/BACKTESTING_ENGINE_TEST_REPORT.md" "Backtesting test report"
    safe_remove "docs/BACKTESTING_FLOWCHART.md" "Backtesting flowchart"
    safe_remove "docs/BACKTESTING_SUMMARY.md" "Backtesting summary"
    safe_remove "docs/COMPARISON_RESULTS.md" "Comparison results"
    safe_remove "docs/COMPREHENSIVE_BACKTESTING_TEST_REPORT.md" "Comprehensive test report"
    safe_remove "docs/DOCKER_COMPLETE_CONTROL_SUMMARY.md" "Docker control summary"
    safe_remove "docs/DOCKER_CONTROL_GUIDE.md" "Docker control guide"
    safe_remove "docs/DOCKER_IMPLEMENTATION_SUMMARY.md" "Docker implementation summary"
    safe_remove "docs/ENHANCED_BACKTESTING.md" "Enhanced backtesting docs"
    safe_remove "docs/FREQTRADE_COMPARISON.md" "FreqTrade comparison"
    safe_remove "docs/FRONTEND_INTEGRATION.md" "Frontend integration docs"
    safe_remove "docs/FYERS_INTEGRATION_SUMMARY.md" "Fyers integration summary"
    safe_remove "docs/FYERS_WEBSOCKET_GUIDE.md" "Fyers WebSocket guide"
    safe_remove "docs/GITHUB_UPLOAD_SUMMARY.md" "GitHub upload summary"
    safe_remove "docs/PERFORMANCE_OPTIMIZATION_SUMMARY.md" "Performance optimization summary"
    safe_remove "docs/RAPIDTRADER_DOCKER_GUIDE.md" "RapidTrader Docker guide"
    safe_remove "docs/RAPIDTRADER_FYERS_DOCKER_COMPLETE.md" "RapidTrader Fyers Docker complete"
    safe_remove "docs/RAPIDTRADER_INDICATOR_SYSTEM_ANALYSIS.md" "Indicator system analysis"
    safe_remove "docs/RAPIDTRADER_VS_FREQTRADE_COMPARISON_REPORT.md" "FreqTrade comparison report"
    safe_remove "docs/STANDALONE_MONEY_MANAGEMENT.md" "Standalone money management"
    safe_remove "docs/UNIFIED_STRATEGY_SYSTEM_SUMMARY.md" "Unified strategy summary"
    safe_remove "docs/UNIFIED_SYMBOL_ARCHITECTURE.md" "Unified symbol architecture"
    
    # Remove root-level documentation files
    safe_remove "ACCOMPLISHMENTS_SUMMARY.md" "Accomplishments summary"
    safe_remove "API_INTEGRATION_COMPLETE.md" "API integration complete"
    safe_remove "BROKER_SYMBOL_MANAGEMENT_COMPLETE.md" "Broker symbol management complete"
    safe_remove "DEVELOPER_INTEGRATION_GUIDE.md" "Developer integration guide"
    safe_remove "OPTIMIZATION_SUMMARY.md" "Optimization summary"
}

# Function to remove test files
clean_test_files() {
    log "Cleaning test files..."
    
    # Remove test results
    safe_remove "test/results" "test results directory"
    safe_remove "test/__pycache__" "test cache directory"
    
    # Remove individual test files (keep the test directory structure)
    safe_remove "test_broker_symbol_manager.py" "broker symbol manager test"
    safe_remove "test_enhanced_symbol_manager.py" "enhanced symbol manager test"
    safe_remove "demo_broker_symbol_integration.py" "demo broker symbol integration"
}

# Function to remove development files
clean_development_files() {
    log "Cleaning development files..."
    
    # Remove demo and example directories
    safe_remove "demo" "demo directory"
    safe_remove "examples" "examples directory"
    
    # Remove project-bolt directory
    safe_remove "project-bolt-sb1-zf39cit1" "project-bolt directory"
    
    # Remove frontend directories (if not needed)
    if [ "$KEEP_FRONTEND" != "true" ]; then
        safe_remove "frontend" "frontend directory"
        safe_remove "frontend_v2" "frontend_v2 directory"
    fi
    
    # Remove backup files
    safe_remove "requirements.txt.backup" "requirements backup"
    find . -name "*.bak" -delete 2>/dev/null || true
    find . -name "*.backup" -delete 2>/dev/null || true
    find . -name "*.old" -delete 2>/dev/null || true
}

# Function to clean data files
clean_data_files() {
    log "Cleaning unnecessary data files..."
    
    # Remove large CSV files (keep JSON configs)
    safe_remove "data/symbols/20250516_NSE.csv" "NSE CSV file"
    safe_remove "data/symbols/nse_symbols.bak.20250530_220400" "NSE symbols backup"
    
    # Clean userdata logs and results
    safe_remove "userdata/logs" "userdata logs"
    safe_remove "userdata/results" "userdata results"
    safe_remove "userdata/historical_data" "userdata historical data"
    
    # Recreate essential directories
    mkdir -p userdata/{logs,results,data,config,strategies}
}

# Function to clean build and cache files
clean_build_files() {
    log "Cleaning build and cache files..."
    
    # Remove Python build artifacts
    safe_remove "build" "build directory"
    safe_remove "dist" "dist directory"
    safe_remove "*.egg-info" "egg-info directories"
    
    # Remove virtual environment
    safe_remove "venv" "virtual environment"
    
    # Remove Docker build cache files
    safe_remove ".dockerignore.old" "old dockerignore"
}

# Function to optimize remaining files
optimize_remaining() {
    log "Optimizing remaining files..."
    
    # Remove empty directories
    find . -type d -empty -delete 2>/dev/null || true
    
    # Compress large JSON files if possible
    if command -v gzip >/dev/null 2>&1; then
        find data/symbols -name "*.json" -size +1M -exec gzip {} \; 2>/dev/null || true
    fi
}

# Function to show cleanup summary
show_summary() {
    log "Cleanup Summary:"
    echo ""
    
    # Show remaining directory sizes
    printf "%-30s %-15s\n" "DIRECTORY" "SIZE"
    printf "%-30s %-15s\n" "---------" "----"
    
    for dir in core broker data api_gateway config money_management security telemetry ui_module userdata; do
        if [ -d "$dir" ]; then
            local size=$(get_size "$dir")
            printf "%-30s %-15s\n" "$dir/" "$size"
        fi
    done
    
    echo ""
    
    # Show total project size
    local total_size=$(get_size ".")
    info "Total project size: $total_size"
    
    echo ""
    info "Essential directories kept:"
    echo "  ✅ core/ - Core trading engine"
    echo "  ✅ broker/ - Broker integrations"
    echo "  ✅ data/ - Data management"
    echo "  ✅ api_gateway/ - API services"
    echo "  ✅ config/ - Configuration"
    echo "  ✅ money_management/ - Risk management"
    echo "  ✅ security/ - Security modules"
    echo "  ✅ telemetry/ - Logging and monitoring"
    echo "  ✅ ui_module/ - Web interface"
    echo "  ✅ userdata/ - User configurations"
    echo "  ✅ scripts/ - Essential scripts"
    echo ""
    
    info "Removed unnecessary files:"
    echo "  🗑️  Documentation files (docs/)"
    echo "  🗑️  Test files and results"
    echo "  🗑️  Demo and example code"
    echo "  🗑️  Frontend development files"
    echo "  🗑️  Python cache files"
    echo "  🗑️  Build artifacts"
    echo "  🗑️  Virtual environment"
    echo "  🗑️  Large data files"
}

# Main execution
main() {
    log "🧹 RapidTrader Project Cleanup"
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --keep-frontend)
                KEEP_FRONTEND=true
                shift
                ;;
            --keep-tests)
                KEEP_TESTS=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --keep-frontend    Keep frontend directories"
                echo "  --keep-tests       Keep test files"
                echo "  --dry-run          Show what would be removed without actually removing"
                echo "  --help             Show this help"
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    if [ "$DRY_RUN" = "true" ]; then
        warn "DRY RUN MODE - No files will be actually removed"
    fi
    
    # Get initial project size
    local initial_size=$(get_size ".")
    info "Initial project size: $initial_size"
    echo ""
    
    # Perform cleanup
    clean_python_cache
    clean_documentation
    
    if [ "$KEEP_TESTS" != "true" ]; then
        clean_test_files
    fi
    
    clean_development_files
    clean_data_files
    clean_build_files
    optimize_remaining
    
    # Show summary
    show_summary
    
    # Calculate space saved
    local final_size=$(get_size ".")
    info "Final project size: $final_size"
    
    log "✅ Project cleanup completed!"
    echo ""
    info "Next steps:"
    echo "  1. Build optimized Docker image: ./scripts/build-simple.sh"
    echo "  2. Test the build: docker run rapidtrader:optimized help"
    echo "  3. Deploy: docker-compose -f docker-compose.optimized.yml up"
}

# Execute main function
main "$@"
