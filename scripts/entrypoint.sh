#!/bin/bash

# RapidTrader Optimized Docker Entrypoint Script
# Ultra-fast initialization and execution with performance optimizations

set -e

# Performance optimizations
export MALLOC_TRIM_THRESHOLD_=100000
export MALLOC_MMAP_THRESHOLD_=131072
export PYTHONOPTIMIZE=2
export PYTHONUNBUFFERED=1
export PYTHONDONTWRITEBYTECODE=1

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Optimized logging function
log() {
    printf "${GREEN}[$(date +'%H:%M:%S')] %s${NC}\n" "$1"
}

error() {
    printf "${RED}[$(date +'%H:%M:%S')] ERROR: %s${NC}\n" "$1" >&2
}

warn() {
    printf "${YELLOW}[$(date +'%H:%M:%S')] WARNING: %s${NC}\n" "$1"
}

info() {
    printf "${BLUE}[$(date +'%H:%M:%S')] INFO: %s${NC}\n" "$1"
}

# Fast directory check with minimal operations
check_directories() {
    local base_dirs=("userdata" "userdata/config" "userdata/logs" "userdata/data" "cache")

    for dir in "${base_dirs[@]}"; do
        [ ! -d "/rapidtrader/$dir" ] && mkdir -p "/rapidtrader/$dir"
    done
}

# Set default config file if not provided
if [ -z "$CONFIG_FILE" ]; then
    CONFIG_FILE="/rapidtrader/userdata/config/config.json"
fi

# Check if we're running in a specific mode
MODE=${1:-help}

# Fast initialization
check_directories

case $MODE in
    # Help mode - show available commands
    "help")
        cat << 'EOF'
RapidTrader Optimized Container

Commands:
  backtest [--optimized]     - High-performance backtesting
  dryrun [--optimized]       - Optimized dry trading
  live [--optimized]         - Fast live trading
  optimize                   - Strategy optimization
  shell                      - Interactive shell
  data                       - Data management
  trade                      - Trading commands
  paper-trade               - Independent paper trading

Optimizations:
  --optimized               - Enable all performance optimizations
  --batch-orders            - Enable order batching
  --async-data              - Enable async data processing
  --fast-execution          - Enable fast execution mode

Examples:
  docker run rapidtrader:optimized backtest --optimized
  docker run rapidtrader:optimized dryrun --optimized --batch-orders
  docker run rapidtrader:optimized live --optimized --fast-execution
EOF
        ;;

    # Backtesting mode
    "backtest")
        log "🔄 Optimized backtesting"
        shift
        if [[ "$*" == *"--optimized"* ]]; then
            export RAPIDTRADER_OPTIMIZATION=true
            export RAPIDTRADER_CACHE_SIZE=2000
            exec python -m core.optimized_backtest_engine "$@"
        else
            exec python /rapidtrader/rapidtrader backtest run -c $CONFIG_FILE "$@"
        fi
        ;;

    # Dry-run mode (traditional broker-based)
    "dryrun")
        log "📊 Optimized dry run"
        shift
        if [[ "$*" == *"--optimized"* ]]; then
            export RAPIDTRADER_BATCH_ORDERS=true
            export RAPIDTRADER_ASYNC_DATA=true
            exec python -c "
import asyncio
import sys
sys.path.insert(0, '/rapidtrader')
from core.dryrun import DryRunEngine

async def main():
    engine = DryRunEngine()
    await engine.run_optimized()

asyncio.run(main())
" "$@"
        else
            exec python /rapidtrader/rapidtrader trade dryrun -c $CONFIG_FILE "$@"
        fi
        ;;

    # Paper trading mode (independent)
    "paper-trade"|"paper")
        log "📝 Independent paper trading"
        shift
        exec python /rapidtrader/rapidtrader paper-trade start -c $CONFIG_FILE "$@"
        ;;

    # Live trading mode
    "live")
        log "💰 Optimized live trading"
        warn "⚠️  LIVE TRADING - REAL MONEY"
        shift
        if [[ "$*" == *"--optimized"* ]]; then
            export RAPIDTRADER_FAST_EXECUTION=true
            export RAPIDTRADER_CONNECTION_POOL=true
            exec python /rapidtrader/rapidtrader trade start --optimized -c $CONFIG_FILE "$@"
        else
            exec python /rapidtrader/rapidtrader trade start -c $CONFIG_FILE "$@"
        fi
        ;;

    # Optimization mode
    "optimize")
        log "⚡ Strategy optimization"
        shift
        exec python /rapidtrader/rapidtrader backtest optimize -c $CONFIG_FILE "$@"
        ;;

    # Data management
    "data")
        log "📈 Data management"
        shift
        exec python -m data.download_data "$@"
        ;;

    # Shell mode
    "shell")
        log "🐚 Interactive shell"
        exec /bin/bash
        ;;

    # Pass any other commands directly to rapidtrader.py
    *)
        exec python /rapidtrader/rapidtrader "$@"
        ;;
esac
